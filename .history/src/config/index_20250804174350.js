/**
 * ZJSC 证据审查系统 - 配置模块统一入口
 * 
 * 功能: 统一导出所有配置模块，提供便捷的配置访问接口
 * 
 * 包含的配置模块：
 * - 证据分类配置 (Module 0b43)
 * - 案件档案配置 (Module 2386)
 * - 审查模式配置 (Module 548b)
 * - 审查项目配置 (Module bf6f)
 */

// 导入所有配置模块
import evidenceTypesConfig from './evidence-types'
import caseArchiveConfig from './case-archive'
import reviewModesConfig from './review-modes'
import reviewItemsConfig from './review-items'
import reviewProcessConfig from './review-process'

/**
 * 统一的配置对象
 */
export const config = {
  // 证据分类配置
  evidenceTypes: evidenceTypesConfig,
  
  // 案件档案配置
  caseArchive: caseArchiveConfig,
  
  // 审查模式配置
  reviewModes: reviewModesConfig,
  
  // 审查项目配置
  reviewItems: reviewItemsConfig
}

/**
 * 配置常量汇总
 */
export const CONFIG_CONSTANTS = {
  // 证据类型常量
  EVIDENCE_TYPES: evidenceTypesConfig.EVIDENCE_TYPE_CONSTANTS,
  
  // 案件档案常量
  CASE_ARCHIVE: caseArchiveConfig.CASE_ARCHIVE_CONSTANTS,
  
  // 审查模式常量
  REVIEW_MODES: reviewModesConfig.REVIEW_MODE_CONSTANTS,
  
  // 审查项目常量
  REVIEW_ITEMS: reviewItemsConfig.REVIEW_ITEM_CONSTANTS
}

/**
 * 配置管理器类
 * 提供统一的配置访问和管理接口
 */
export class ConfigManager {
  constructor() {
    this.config = config
    this.constants = CONFIG_CONSTANTS
  }

  /**
   * 获取证据类型树形结构
   * @returns {Array} 证据类型树
   */
  getEvidenceTypeTree() {
    return this.config.evidenceTypes.getEvidenceTypeTree()
  }

  /**
   * 获取案件档案树形结构
   * @returns {Array} 案件档案树
   */
  getCaseArchiveTree() {
    return this.config.caseArchive.getCaseArchiveTree()
  }

  /**
   * 获取所有审查模式
   * @returns {Array} 审查模式数组
   */
  getAllReviewModes() {
    return this.config.reviewModes.getAllReviewModes()
  }

  /**
   * 获取所有审查项目
   * @returns {Array} 审查项目数组
   */
  getAllReviewItems() {
    return this.config.reviewItems.getAllReviewItems()
  }

  /**
   * 根据模式编码获取审查模式配置
   * @param {string} modeCode - 模式编码
   * @returns {Object|null} 审查模式配置
   */
  getReviewModeConfig(modeCode) {
    return this.config.reviewModes.getModeConfig(modeCode)
  }

  /**
   * 根据ID获取证据类型
   * @param {string} id - 证据类型ID
   * @returns {Object|null} 证据类型信息
   */
  getEvidenceTypeById(id) {
    return this.config.evidenceTypes.getEvidenceTypeById(id)
  }

  /**
   * 根据ID获取档案节点
   * @param {string} id - 档案节点ID
   * @returns {Object|null} 档案节点信息
   */
  getArchiveNodeById(id) {
    return this.config.caseArchive.getArchiveNodeById(id)
  }

  /**
   * 根据ID获取审查项目
   * @param {string} scxbh - 审查项编号
   * @returns {Object|null} 审查项目配置
   */
  getReviewItemById(scxbh) {
    return this.config.reviewItems.getReviewItemById(scxbh)
  }

  /**
   * 创建审查项目
   * @param {string} templateType - 模板类型
   * @param {Object} customData - 自定义数据
   * @returns {Object} 新的审查项目
   */
  createReviewItem(templateType, customData = {}) {
    return this.config.reviewItems.createReviewItemFromTemplate(templateType, customData)
  }

  /**
   * 验证审查项目
   * @param {Object} reviewItem - 审查项目
   * @returns {Object} 验证结果
   */
  validateReviewItem(reviewItem) {
    return this.config.reviewItems.validateReviewItem(reviewItem)
  }

  /**
   * 获取配置统计信息
   * @returns {Object} 配置统计信息
   */
  getConfigStats() {
    return {
      evidenceTypesCount: this.config.evidenceTypes.evidenceTypes.length,
      caseArchiveNodesCount: this.config.caseArchive.caseArchiveStructure.length,
      reviewModesCount: this.config.reviewModes.reviewModes.length,
      reviewItemsCount: this.config.reviewItems.reviewItemsConfig.length,
      templateTypesCount: this.config.reviewItems.getAvailableTemplateTypes().length
    }
  }

  /**
   * 重新加载配置
   * @returns {Promise<void>}
   */
  async reloadConfig() {
    // 这里可以实现从服务器重新加载配置的逻辑
    console.log('配置重新加载完成')
  }

  /**
   * 导出配置为JSON
   * @returns {string} JSON格式的配置数据
   */
  exportConfigAsJSON() {
    return JSON.stringify(this.config, null, 2)
  }

  /**
   * 从JSON导入配置
   * @param {string} jsonString - JSON格式的配置数据
   * @returns {boolean} 是否导入成功
   */
  importConfigFromJSON(jsonString) {
    try {
      const importedConfig = JSON.parse(jsonString)
      // 这里可以添加配置验证逻辑
      this.config = { ...this.config, ...importedConfig }
      return true
    } catch (error) {
      console.error('配置导入失败:', error)
      return false
    }
  }
}

// 创建全局配置管理器实例
export const configManager = new ConfigManager()

/**
 * 便捷的配置访问函数
 */

// 证据类型相关
export const getEvidenceTypeTree = () => configManager.getEvidenceTypeTree()
export const getEvidenceTypeById = (id) => configManager.getEvidenceTypeById(id)

// 案件档案相关
export const getCaseArchiveTree = () => configManager.getCaseArchiveTree()
export const getArchiveNodeById = (id) => configManager.getArchiveNodeById(id)

// 审查模式相关
export const getAllReviewModes = () => configManager.getAllReviewModes()
export const getReviewModeConfig = (modeCode) => configManager.getReviewModeConfig(modeCode)

// 审查项目相关
export const getAllReviewItems = () => configManager.getAllReviewItems()
export const getReviewItemById = (scxbh) => configManager.getReviewItemById(scxbh)
export const createReviewItem = (templateType, customData) => configManager.createReviewItem(templateType, customData)

// 重新导出各个配置模块（保持向后兼容）
export { default as evidenceTypes } from './evidence-types'
export { default as caseArchive } from './case-archive'
export { default as reviewModes } from './review-modes'
export { default as reviewItems } from './review-items'

// 默认导出配置管理器
export default configManager
