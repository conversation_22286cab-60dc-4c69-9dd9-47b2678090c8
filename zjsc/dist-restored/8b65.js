/**
 * 微前端生命周期管理模块
 * Module ID: 8b65
 * 
 * 这个模块负责管理微前端应用的生命周期函数，包括：
 * - bootstrap: 应用启动初始化
 * - mount: 应用挂载
 * - unmount: 应用卸载
 * - update: 应用更新
 * 
 * 该模块是 qiankun 微前端框架的标准生命周期实现
 */

import { store } from '@/store'
import { router } from '@/router'
import App from '@/App.vue'
import Vue from 'vue'
import utils from '@/utils'

// Vue 应用实例
let vueInstance = null

/**
 * 创建并挂载 Vue 应用实例
 * @param {Object} props - 微前端传递的属性
 */
function createApp(props) {
  vueInstance = new Vue({
    router,
    store,
    render: h => h(App),
  }).$mount('#app')
}

/**
 * 微前端生命周期 - 启动初始化
 * @param {Object} props - 微前端传递的属性
 * @returns {Promise<void>}
 */
export async function bootstrap(props) {
  console.log('vue sub app bootstrap ', props)
}

/**
 * 微前端生命周期 - 应用挂载
 * @param {Object} props - 微前端传递的属性，包含全局状态管理方法
 * @returns {Promise<void>}
 */
export async function mount(props) {
  // 如果有全局状态管理，设置相关方法
  if (props && utils.setActions) {
    utils.setActions(props)
    
    // 监听全局状态变化
    props.onGlobalStateChange((newState, prevState) => {
      console.log('state: 变更后的状态; prev 变更前的状态')
      console.log(newState, prevState)
    })
    
    // 设置全局状态
    props.setGlobalState({
      event: 'opendialog',
    })
  }
  
  // 创建并挂载应用
  createApp(props)
}

/**
 * 微前端生命周期 - 应用卸载
 * @param {Object} props - 微前端传递的属性
 * @returns {Promise<void>}
 */
export async function unmount(props) {
  if (vueInstance) {
    vueInstance.$destroy()
    vueInstance = null
  }
}

/**
 * 微前端生命周期 - 应用更新
 * @param {Object} props - 微前端传递的属性
 * @returns {Promise<void>}
 */
export async function update(props) {
  console.log('update props', props)
}

// 如果不是在 qiankun 环境中运行，直接挂载应用
if (!window.__POWERED_BY_QIANKUN__) {
  createApp()
}
