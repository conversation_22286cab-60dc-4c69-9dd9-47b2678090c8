/**
 * ZJSC主应用模块 - Vue根组件+微前端生命周期
 * @description 这是ZJSC证据审查系统的核心模块，包含Vue应用、路由配置、状态管理和微前端生命周期
 * <AUTHOR>
 * @module MainApplicationModule
 */

import Vue from 'vue'
import VueRouter from 'vue-router'
import Vuex from 'vuex'
import { qiankunActions } from '@/utils/qiankun'

// Vue.js配置
Vue.use(VueRouter)
Vue.use(Vuex)

/**
 * Vue根组件App
 * @description 应用的根组件，包含路由视图
 */
const AppComponent = {
  name: "App",
  template: `
    <div class="App">
      <router-view />
    </div>
  `,
  data() {
    return {};
  },
  mounted() {},
  methods: {}
}

/**
 * 路由配置
 * @description 定义应用的路由表，支持懒加载
 */
const routes = [
  {
    path: "/",
    name: "index",
    component: () => import(/* webpackChunkName: "review-main" */ '@/views/ReviewMain.vue'),
  },
  {
    path: "/test",
    name: "test", 
    component: () => import(/* webpackChunkName: "mindmap-test" */ '@/views/MindMapTest.vue'),
  },
  {
    path: "/404",
    name: "404",
    component: () => import(/* webpackChunkName: "not-found" */ '@/views/NotFound.vue'),
  }
]

/**
 * 路由实例配置
 * @description 支持微前端和独立运行两种模式
 */
const router = new VueRouter({
  mode: "history",
  base: window.__POWERED_BY_QIANKUN__ ? "/fzba/zjsc/" : "/zjsc",
  routes: routes,
})

/**
 * 路由守卫
 * @description 检查用户认证状态和权限
 */
router.beforeEach((to, from, next) => {
  const vuexData = JSON.parse(sessionStorage.getItem("vuex"))
  const publicPaths = ["/", "/index", "/404"]
  
  if (publicPaths.indexOf(to.path) < 0) {
    if (!vuexData) {
      return next("/")
    }
  }
  next()
})

/**
 * Vuex状态管理配置
 * @description 管理应用全局状态，支持持久化
 */
const store = new Vuex.Store({
  state: {
    personInfo: {},       // 人员信息
    caseInfo: {},        // 案件信息  
    storeRightWidth: 50, // 右侧面板宽度
  },
  mutations: {
    SET_STORERIGHTWIDTH(state, width) {
      state.storeRightWidth = width
    },
  },
  actions: {
    /**
     * 更新右侧面板宽度
     * @param {Function} commit - Vuex commit函数
     * @param {number} width - 新的宽度值
     */
    updateStoreRightWidth({ commit }, width) {
      commit("SET_STORERIGHTWIDTH", width)
      
      // 向父应用发送宽度变化事件
      window.dispatchEvent(
        new CustomEvent("childStoreRightWidthChange", {
          detail: { width },
        })
      )
    },
  },
  plugins: [
    // 状态持久化插件
    createPersistedState({
      storage: window.sessionStorage,
    }),
  ],
})

// 监听父应用的宽度变化事件
window.addEventListener("storeRightWidthChange", (event) => {
  store.commit("SET_STORERIGHTWIDTH", event.detail.width)
})

/**
 * 日期格式化工具函数
 * @description 提供多种日期格式化选项
 */
const dateUtils = {
  /**
   * 格式化日期
   * @param {Date} date - 要格式化的日期
   * @param {string} format - 格式类型 (1-4)
   * @returns {string} 格式化后的日期字符串
   */
  formatDate(date, format) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    switch (format) {
      case "1":
        return `${year}-${month}-${day}`
      case "2":
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      case "3":
        return `${year}年${month}月${day}日`
      case "4":
        return `${year}年${month}月${day}日 ${hours}时${minutes}分${seconds}秒`
      default:
        return `${year}-${month}-${day}`
    }
  },
}

// Vue应用实例
let vueApp = null

/**
 * 渲染Vue应用
 * @description 创建Vue实例并挂载到DOM
 * @param {Object} props - 微前端传入的props
 */
function renderApp(props = {}) {
  vueApp = new Vue({
    router,
    store,
    render: h => h(AppComponent),
  }).$mount("#app")
}

/**
 * 微前端生命周期函数 - bootstrap
 * @description 应用启动时的初始化钩子
 * @param {Object} props - 父应用传入的props
 */
export async function bootstrap(props) {
  console.log("ZJSC子应用启动中...", props)
}

/**
 * 微前端生命周期函数 - mount
 * @description 应用挂载时的钩子，设置全局通信
 * @param {Object} props - 父应用传入的props
 */
export async function mount(props) {
  if (props) {
    // 设置全局状态管理
    qiankunActions.setActions(props)
    
    // 监听全局状态变化
    props.onGlobalStateChange?.((newState, prevState) => {
      console.log("全局状态变化:", { newState, prevState })
    })
    
    // 设置全局状态
    props.setGlobalState?.({
      event: "opendialog",
    })
  }
  
  // 渲染应用
  renderApp(props)
}

/**
 * 微前端生命周期函数 - unmount  
 * @description 应用卸载时的清理钩子
 * @param {Object} props - 父应用传入的props
 */
export async function unmount(props) {
  if (vueApp) {
    vueApp.$destroy()
    vueApp = null
  }
}

/**
 * 微前端生命周期函数 - update
 * @description 应用更新时的钩子
 * @param {Object} props - 父应用传入的props
 */
export async function update(props) {
  console.log("ZJSC子应用更新props:", props)
}

// 根据运行环境决定启动方式
if (window.__POWERED_BY_QIANKUN__) {
  // 微前端模式：设置动态公共路径
  __webpack_public_path__ = window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__
} else {
  // 独立运行模式：直接启动应用
  renderApp()
}

// 导出工具函数供其他模块使用
export { dateUtils, router, store, AppComponent }