/**
 * 树形数据处理工具类
 * Module ID: f382
 * 
 * 这个模块提供了树形数据结构的处理工具，主要用于：
 * - 将扁平数组转换为树形结构
 * - 遍历树形结构并转换为扁平数组或对象
 * 
 * 在 ZJSC 项目中主要用于处理审查项目的层级结构和案件档案的目录结构
 */

/**
 * 树形数据处理工具类
 */
export class TreeUtil {
  /**
   * 将扁平数组转换为树形结构
   * @param {Array} flatArray - 扁平数组数据源
   * @param {string} idField - ID 字段名，默认为 'id'
   * @param {string} parentIdField - 父级 ID 字段名，默认为 'pid'
   * @param {Object} nodeMap - 节点映射对象，用于优化查找性能
   * @returns {Array} 树形结构数组
   * 
   * @example
   * const flatData = [
   *   { id: '1', pid: '', name: '根节点' },
   *   { id: '2', pid: '1', name: '子节点1' },
   *   { id: '3', pid: '1', name: '子节点2' }
   * ];
   * const treeData = TreeUtil.getTreeData(flatData);
   */
  static getTreeData(flatArray, idField = 'id', parentIdField = 'pid', nodeMap = {}) {
    // 验证输入参数
    if (!Array.isArray(flatArray)) {
      console.warn('TreeUtil.getTreeData: 输入数据必须是数组')
      return []
    }

    const rootNodes = []
    
    // 第一遍遍历：建立节点映射
    for (const node of flatArray) {
      nodeMap[node[idField]] = node
    }
    
    // 第二遍遍历：建立父子关系
    for (const node of flatArray) {
      const parentNode = nodeMap[node[parentIdField]]
      
      if (parentNode) {
        // 有父节点，添加到父节点的 children 数组中
        if (!parentNode.children) {
          parentNode.children = []
        }
        parentNode.children.push(node)
      } else {
        // 没有父节点，作为根节点
        rootNodes.push(node)
      }
    }
    
    return rootNodes
  }

  /**
   * 遍历树形结构，将其转换为扁平数组或对象映射
   * @param {Array} treeArray - 树形结构数组
   * @param {string} returnType - 返回类型：'array' 返回数组，'object' 返回对象映射
   * @param {string} idField - ID 字段名，默认为 'id'
   * @returns {Array|Object} 扁平化后的数组或对象映射
   * 
   * @example
   * // 返回扁平数组
   * const flatArray = TreeUtil.findAllNode(treeData, 'array');
   * 
   * // 返回对象映射
   * const nodeMap = TreeUtil.findAllNode(treeData, 'object', 'id');
   */
  static findAllNode(treeArray, returnType = 'array', idField = 'id') {
    // 验证输入参数
    if (!Array.isArray(treeArray)) {
      console.info('TreeUtil.findAllNode: 传入数据源非数组')
      return []
    }

    // 深拷贝输入数据，避免修改原始数据
    const clonedData = JSON.parse(JSON.stringify(treeArray))
    
    // 使用栈进行深度优先遍历（反向压栈以保持原始顺序）
    const stack = [...clonedData.reverse()]
    const resultArray = []
    const resultObject = {}

    while (stack.length > 0) {
      const currentNode = stack.pop()
      
      // 根据返回类型存储节点
      if (returnType === 'array') {
        resultArray.push(currentNode)
      } else {
        resultObject[currentNode[idField]] = currentNode
      }
      
      // 如果当前节点有子节点，将子节点压入栈中（反向压栈）
      if (currentNode.children && currentNode.children.length > 0) {
        stack.push(...currentNode.children.reverse())
      }
    }
    
    return returnType === 'array' ? resultArray : resultObject
  }

  /**
   * 在树形结构中查找指定节点
   * @param {Array} treeArray - 树形结构数组
   * @param {string|Function} condition - 查找条件，可以是字段值或判断函数
   * @param {string} field - 查找字段名（当 condition 为字符串时使用）
   * @returns {Object|null} 找到的节点或 null
   */
  static findNode(treeArray, condition, field = 'id') {
    if (!Array.isArray(treeArray)) return null

    const stack = [...treeArray]
    
    while (stack.length > 0) {
      const currentNode = stack.pop()
      
      // 检查当前节点是否匹配条件
      const isMatch = typeof condition === 'function' 
        ? condition(currentNode)
        : currentNode[field] === condition
      
      if (isMatch) {
        return currentNode
      }
      
      // 将子节点加入搜索栈
      if (currentNode.children && currentNode.children.length > 0) {
        stack.push(...currentNode.children)
      }
    }
    
    return null
  }

  /**
   * 获取节点的所有祖先节点路径
   * @param {Array} treeArray - 树形结构数组
   * @param {string} targetId - 目标节点 ID
   * @param {string} idField - ID 字段名
   * @returns {Array} 祖先节点路径数组
   */
  static getNodePath(treeArray, targetId, idField = 'id') {
    const path = []
    
    function findPath(nodes, target, currentPath) {
      for (const node of nodes) {
        const newPath = [...currentPath, node]
        
        if (node[idField] === target) {
          path.push(...newPath)
          return true
        }
        
        if (node.children && findPath(node.children, target, newPath)) {
          return true
        }
      }
      return false
    }
    
    findPath(treeArray, targetId, [])
    return path
  }
}

// 默认导出
export default TreeUtil
