/**
 * 应用程序入口模块
 * @description 这是 ZJSC (证据审查) Vue 应用的根入口模块，负责引导整个应用的启动
 * <AUTHOR> 开发团队
 * @module EntryModule
 */

/**
 * 应用入口点函数
 * @param {Object} moduleExports - 模块导出对象
 * @param {Object} moduleContext - 模块上下文 
 * @param {Function} webpackRequire - Webpack require 函数
 * @returns {Object} 导出的主应用模块
 */
function applicationEntryPoint(moduleExports, moduleContext, webpackRequire) {
  // 直接导出主应用模块 (56d7)，该模块包含 Vue 应用和微前端生命周期
  moduleExports.exports = webpackRequire("56d7");
}

// 模块元信息
const MODULE_INFO = {
  id: "0",
  name: "ApplicationEntry", 
  type: "EntryPoint",
  description: "ZJSC证据审查应用的Webpack入口模块",
  dependencies: ["56d7"], // 依赖主应用模块
  exports: ["bootstrap", "mount", "unmount", "update"], // 微前端生命周期函数
  framework: "Vue.js + qiankun微前端"
};

export default applicationEntryPoint;