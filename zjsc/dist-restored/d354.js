/**
 * HTTP 客户端封装模块
 * Module ID: d354
 * 
 * 这个模块基于 Axios 封装了 HTTP 客户端，提供了：
 * - 请求和响应拦截器
 * - 统一的错误处理
 * - Token 自动管理
 * - 特殊接口白名单处理
 * 
 * 在 ZJSC 项目中作为所有 API 请求的基础服务
 */

import axios from 'axios'
import { Message, MessageBox } from 'element-ui'
import router from '@/router'

// 不需要统一错误处理的接口白名单
let whitelistUrls = ['/file/dowload']

/**
 * 添加接口到白名单
 * @param {Array} urls - 需要添加到白名单的 URL 数组
 */
export const addWhitelistUrls = (urls) => {
  const urlSet = new Set([...whitelistUrls, ...urls])
  whitelistUrls = Array.from(urlSet)
}

/**
 * 设置 Axios 基础 URL
 * 从全局菜单配置中获取服务地址
 */
function setBaseURL() {
  if (!window.mock) {
    const menuList = window.menuList
    const zjscMenu = menuList?.find(menu => menu.xtbs === 'zjsc')
    const baseURL = zjscMenu?.yyfwUrl || ''
    
    if (baseURL) {
      axios.defaults.baseURL = baseURL
    }
  }
}

// 初始化基础 URL
setBaseURL()

/**
 * 创建 Axios 实例
 */
const httpClient = axios.create({
  timeout: 60000, // 60秒超时
})

/**
 * 请求拦截器
 * 自动添加 Authorization 头
 */
httpClient.interceptors.request.use(
  (config) => {
    const token = window.sessionStorage.getItem('token')
    config.headers['authorization'] = `Bearer ${token || ''}`
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

/**
 * 错误处理函数
 * @param {number} code - 错误码
 * @param {Object} errorConfig - 错误配置对象
 */
function handleError(code, errorConfig) {
  switch (errorConfig.handler) {
    case 'message':
      Message({
        type: errorConfig.type === 'warning' ? 'warning' : 'error',
        message: errorConfig.msg,
      })
      break
      
    case 'toLogin':
      // 检查是否已有弹窗，避免重复弹出
      const existingMessageBox = document.getElementsByClassName('el-overlay-message-box')
      if (existingMessageBox && existingMessageBox.length > 0) {
        return
      }
      
      MessageBox.confirm(errorConfig.msg, '警告', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
      }).then(() => {
        router.push('/')
      }).catch(() => {
        // 用户取消，不做处理
      })
      break
      
    default:
      break
  }
}

/**
 * 响应拦截器
 * 统一处理响应数据和错误
 */
httpClient.interceptors.response.use(
  (response) => {
    // 白名单接口直接返回原始数据
    if (response.config.url && whitelistUrls.indexOf(response.config.url) >= 0) {
      return Promise.resolve(response.data)
    }
    
    // 成功响应处理
    if (response.data.code === 200) {
      // 自动更新 token
      if (response.data.data?.token) {
        window.sessionStorage.setItem('token', response.data.data.token)
        response.data.data.token = '' // 清空返回数据中的 token
      }
      
      return Promise.resolve(response.data.data)
    }
    
    // 错误响应处理
    console.log('请求失败:', response.data.code, response.config.baseURL, response.config.url)
    console.log('返回值', response.data)
    
    const errorCode = response.data.code === -1 ? '_1' : response.data.code
    const errorConfig = ERROR_CODES[`CODE_${errorCode}`]
    const errorMessage = errorConfig?.msg || ''
    
    if (errorMessage) {
      handleError(response.data.code, errorConfig)
    }
    
    return Promise.reject({
      code: response.data.code,
      isError: !!errorMessage,
      message: response.data.message,
    })
  },
  (error) => {
    const statusCode = error.response?.status || ''
    const errorConfig = ERROR_CODES[`CODE_${statusCode}`]
    const errorMessage = errorConfig?.msg || error.code
    
    return Promise.reject({
      isError: false,
      code: error.response ? error.response.status : '',
      message: error.response?.data?.message || error.message,
    })
  }
)

/**
 * 错误码配置映射
 */
const ERROR_CODES = {
  // 系统异常
  CODE__1: {
    msg: '程序运行发生异常，请联系管理员',
    handler: 'message',
  },
  
  // HTTP 状态码错误
  CODE_400: {
    msg: '请求错误(400)，请联系管理员',
    handler: 'message',
  },
  CODE_401: {
    msg: '未授权，请重新登录(401)，请联系管理员',
    handler: 'toLogin',
  },
  CODE_403: {
    msg: '拒绝访问(403)，请联系管理员',
    handler: 'message',
  },
  CODE_404: {
    msg: '未找到(404)，请联系管理员',
    handler: 'message',
  },
  CODE_408: {
    msg: '请求超时(408)，请联系管理员',
    handler: 'message',
  },
  CODE_500: {
    msg: '服务器错误(500)，请联系管理员',
    handler: 'message',
  },
  CODE_501: {
    msg: '服务未实现(501)，请联系管理员',
    handler: 'message',
  },
  CODE_502: {
    msg: '网络错误(502)，请联系管理员',
    handler: 'message',
  },
  CODE_503: {
    msg: '服务不可用(503)，请联系管理员',
    handler: 'message',
  },
  CODE_504: {
    msg: '网络超时(504)，请联系管理员',
    handler: 'message',
  },
  CODE_505: {
    msg: 'HTTP版本不受支持(505)，请联系管理员',
    handler: 'message',
  },
  
  // 业务错误码
  CODE_40100: {
    msg: '登录状态无效，请重新登录',
    handler: 'toLogin',
  },
  CODE_40000: {
    msg: '请求不合法',
    handler: 'message',
    type: 'warning',
  },
  CODE_40300: {
    msg: '您已被禁止登录',
    handler: 'message',
    type: 'warning',
  },
  CODE_40003: {
    msg: '参数无效，请重新输入',
    handler: 'message',
  },
  CODE_40101: {
    msg: '登录状态无效，请重新登录',
    handler: 'toLogin',
  },
  CODE_40102: {
    msg: '登录状态已过期，请重新登录',
    handler: 'toLogin',
  },
  CODE_40103: {
    msg: '您已在其他地方登录，如果在该设备访问，请重新登录',
    handler: 'toLogin',
  },
  CODE_40104: {
    msg: '登录状态无效，请重新登录',
    handler: 'toLogin',
  },
  CODE_40005: {
    msg: '参数不合法',
    handler: 'message',
  },
  CODE_40306: {
    msg: '您没有权限访问',
    handler: 'toLogin',
  },
  CODE_40307: {
    msg: '您没有权限访问',
    handler: 'toLogin',
  },
  CODE_99998: {
    msg: '请求取消后报的异常，无需处理',
    handler: '',
  },
  CODE_99999: {
    msg: '返回参数无效，请联系管理员',
    handler: 'message',
  },
}

// 导出 HTTP 客户端实例
export default httpClient
