# Module d354 - HTTP 客户端封装

## 📋 模块概览

**模块ID**: d354  
**功能**: HTTP 客户端封装  
**类型**: 网络服务模块  
**文件位置**: `app.43514e93.js`  
**还原目标**: `src/api/http.js`

## 🎯 功能分析

### 核心功能
这个模块是 ZJSC 项目的网络请求核心，基于 Axios 封装了完整的 HTTP 客户端服务。

### 主要职责
1. **请求拦截** - 自动添加 Authorization 头
2. **响应拦截** - 统一处理响应数据和错误
3. **错误处理** - 完善的错误码映射和用户提示
4. **Token 管理** - 自动更新和存储 token
5. **白名单机制** - 特殊接口的差异化处理
6. **基础配置** - 动态设置服务地址和超时时间

## 🔍 原始代码分析

### 混淆前的代码结构
```javascript
// 基础配置
const e = t["a"].create({ timeout: 6e4 })

// 请求拦截器
e.interceptors.request.use((s) => {
  const c = window.sessionStorage.getItem("token")
  return ((s.headers["authorization"] = "Bearer " + c || ""), s)
})

// 响应拦截器
e.interceptors.response.use((s) => {
  // 处理成功响应和错误响应
})

// 错误码配置
const z = { CODE__1: { msg: "...", handler: "message" } }
```

### 关键发现
1. **Axios 实例**: 使用 `axios.create()` 创建独立实例
2. **Token 机制**: Bearer Token 认证方式
3. **错误分类**: 按处理方式分为 message 和 toLogin 两类
4. **白名单**: 文件下载等接口不走统一错误处理
5. **动态配置**: 从全局菜单配置获取服务地址

## 🏗️ 还原思路

### 1. 变量和函数重命名
- **实例变量**: `e` → `httpClient`
- **参数重命名**: `s` → `config/response`, `c` → `token/errorConfig`
- **函数重命名**: `j` → `handleError`, `i` → `addWhitelistUrls`

### 2. 模块化重构
- 分离错误码配置为独立对象
- 提取错误处理逻辑为独立函数
- 添加白名单管理功能
- 增强基础 URL 设置逻辑

### 3. 功能增强
- 添加详细的 JSDoc 注释
- 增强错误处理的健壮性
- 优化 token 更新逻辑
- 添加请求取消处理

## 📦 依赖关系

### 输入依赖
- `axios` - HTTP 客户端库
- `element-ui` - Message 和 MessageBox 组件
- `@/router` - Vue Router 实例

### 输出接口
```javascript
export default httpClient           // Axios 实例
export const addWhitelistUrls      // 白名单管理函数
```

### 在项目中的使用
```javascript
// 在 API 服务中使用
import http from '@/api/http'

// 发起请求
const response = await http.get('/api/data')
```

## 🔧 技术特点

### 拦截器机制
- **请求拦截**: 自动添加认证头
- **响应拦截**: 统一数据格式和错误处理
- **错误分类**: 按业务需求分类处理错误

### 安全特性
- **Token 自动更新**: 响应中的新 token 自动存储
- **认证失效处理**: 自动跳转登录页
- **权限控制**: 区分不同权限错误的处理方式

### 用户体验
- **错误提示**: 友好的错误消息提示
- **防重复弹窗**: 避免多个错误弹窗同时出现
- **超时设置**: 60秒请求超时保护

## 💼 业务应用场景

### 1. API 请求封装
```javascript
// 审查数据请求
const reviewData = await http.get('/api/review/list')

// 提交审查结果
await http.post('/api/review/submit', data)
```

### 2. 文件下载处理
```javascript
// 添加文件下载接口到白名单
addWhitelistUrls(['/api/file/download', '/api/export/excel'])
```

### 3. 错误处理示例
```javascript
try {
  const data = await http.get('/api/protected')
} catch (error) {
  if (error.code === 40101) {
    // 自动跳转登录页，无需手动处理
  }
}
```

## ⚠️ 注意事项

### 1. Token 管理
- Token 存储在 sessionStorage 中
- 响应中的新 token 会自动更新
- 认证失效时自动跳转登录

### 2. 错误处理策略
- 业务错误显示消息提示
- 认证错误跳转登录页
- 网络错误显示技术错误信息

### 3. 白名单机制
- 文件下载接口不走统一错误处理
- 可动态添加新的白名单接口
- 白名单接口返回原始响应数据

### 4. 基础配置
- 服务地址从全局菜单配置获取
- Mock 模式下不设置 baseURL
- 60秒请求超时设置

## 🚀 使用示例

### 基本用法
```javascript
import http from '@/api/http'

// GET 请求
const data = await http.get('/api/users')

// POST 请求
const result = await http.post('/api/users', { name: 'John' })

// 带参数的请求
const list = await http.get('/api/users', { params: { page: 1 } })
```

### 高级用法
```javascript
// 添加白名单接口
import { addWhitelistUrls } from '@/api/http'
addWhitelistUrls(['/api/special/endpoint'])

// 自定义请求配置
const response = await http({
  method: 'post',
  url: '/api/upload',
  data: formData,
  headers: { 'Content-Type': 'multipart/form-data' }
})
```

### 错误处理
```javascript
try {
  const data = await http.get('/api/data')
  // 处理成功响应
} catch (error) {
  if (error.isError) {
    // 已经显示了错误提示，无需额外处理
  } else {
    // 网络错误或其他未处理错误
    console.error('请求失败:', error.message)
  }
}
```

## 📈 还原质量评估

- **完整性**: ✅ 完整还原了所有功能特性
- **可读性**: ✅ 清晰的代码结构和注释
- **可维护性**: ✅ 模块化设计，易于扩展
- **安全性**: ✅ 完善的认证和错误处理
- **用户体验**: ✅ 友好的错误提示和防重复机制

---

**还原时间**: 2025-01-04  
**还原状态**: ✅ 完成  
**测试状态**: 待测试  
**优先级**: 🔴 最高
