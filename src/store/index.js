/**
 * ZJSC 证据审查系统 - Vuex 状态管理
 * 
 * Module ID: f382
 * 功能: Vuex Store 配置，包含 personInfo、caseInfo 状态管理
 * 
 * 管理应用的全局状态，包括人员信息、案件信息等核心数据
 */

import Vue from 'vue';
import Vuex from 'vuex';
import createPersistedState from 'vuex-persistedstate';

// 安装Vuex
Vue.use(Vuex);

// ========== 状态定义 ==========

/**
 * 默认状态
 */
const defaultState = {
  // 人员信息
  personInfo: {
    id: '',
    name: '',
    department: '',
    position: '',
    phone: '',
    email: '',
    avatar: '',
    permissions: [],
    roles: []
  },
  
  // 案件信息
  caseInfo: {
    id: '',
    caseNumber: '',
    caseName: '',
    caseType: '',
    status: '',
    createDate: '',
    updateDate: '',
    description: '',
    relatedPersons: [],
    evidenceList: [],
    documents: []
  },
  
  // UI状态
  ui: {
    storeRightWidth: 50, // 右侧面板宽度
    sidebarCollapsed: false, // 侧边栏折叠状态
    theme: 'light', // 主题模式
    language: 'zh-CN', // 语言设置
    loading: false, // 全局loading状态
    notification: {
      show: false,
      message: '',
      type: 'info',
      duration: 3000
    }
  },
  
  // 审查状态
  review: {
    currentStep: 0, // 当前审查步骤
    totalSteps: 5, // 总步骤数
    reviewData: {}, // 审查数据
    selectedItems: [], // 选中的审查项
    reviewHistory: [], // 审查历史
    autoSave: true, // 自动保存
    lastSaved: null // 最后保存时间
  },
  
  // 系统配置
  system: {
    apiBaseUrl: '',
    uploadUrl: '',
    downloadUrl: '',
    maxFileSize: 10 * 1024 * 1024, // 10MB
    supportedFileTypes: ['.pdf', '.doc', '.docx', '.jpg', '.png'],
    version: '1.0.0',
    environment: 'development'
  }
};

// ========== Mutations ==========

const mutations = {
  // 人员信息相关
  SET_PERSON_INFO(state, personInfo) {
    state.personInfo = { ...state.personInfo, ...personInfo };
  },
  RESET_PERSON_INFO(state) {
    state.personInfo = { ...defaultState.personInfo };
  },
  
  // 案件信息相关
  SET_CASE_INFO(state, caseInfo) {
    state.caseInfo = { ...state.caseInfo, ...caseInfo };
  },
  RESET_CASE_INFO(state) {
    state.caseInfo = { ...defaultState.caseInfo };
  },
  
  // UI状态相关
  SET_STORERIGHTWIDTH(state, width) {
    state.ui.storeRightWidth = width;
    // 广播宽度变化事件
    window.dispatchEvent(new CustomEvent('storeRightWidthChange', { detail: width }));
  },
  SET_SIDEBAR_COLLAPSED(state, collapsed) {
    state.ui.sidebarCollapsed = collapsed;
  },
  SET_THEME(state, theme) {
    state.ui.theme = theme;
  },
  SET_LANGUAGE(state, language) {
    state.ui.language = language;
  },
  SET_LOADING(state, loading) {
    state.ui.loading = loading;
  },
  SET_NOTIFICATION(state, notification) {
    state.ui.notification = { ...state.ui.notification, ...notification };
  },
  HIDE_NOTIFICATION(state) {
    state.ui.notification.show = false;
  },
  
  // 审查状态相关
  SET_CURRENT_STEP(state, step) {
    state.review.currentStep = step;
  },
  SET_TOTAL_STEPS(state, steps) {
    state.review.totalSteps = steps;
  },
  SET_REVIEW_DATA(state, data) {
    state.review.reviewData = { ...state.review.reviewData, ...data };
  },
  SET_SELECTED_ITEMS(state, items) {
    state.review.selectedItems = items;
  },
  ADD_SELECTED_ITEM(state, item) {
    state.review.selectedItems.push(item);
  },
  REMOVE_SELECTED_ITEM(state, itemId) {
    state.review.selectedItems = state.review.selectedItems.filter(item => item.id !== itemId);
  },
  SET_REVIEW_HISTORY(state, history) {
    state.review.reviewHistory = history;
  },
  ADD_REVIEW_HISTORY(state, record) {
    state.review.reviewHistory.unshift(record);
  },
  SET_AUTO_SAVE(state, autoSave) {
    state.review.autoSave = autoSave;
  },
  SET_LAST_SAVED(state, timestamp) {
    state.review.lastSaved = timestamp;
  },
  
  // 系统配置相关
  SET_SYSTEM_CONFIG(state, config) {
    state.system = { ...state.system, ...config };
  },
  
  // 重置所有状态
  RESET_ALL_STATE(state) {
    Object.assign(state, defaultState);
  }
};

// ========== Actions ==========

const actions = {
  // 人员信息相关
  updatePersonInfo({ commit }, personInfo) {
    commit('SET_PERSON_INFO', personInfo);
  },
  
  resetPersonInfo({ commit }) {
    commit('RESET_PERSON_INFO');
  },
  
  // 案件信息相关
  updateCaseInfo({ commit }, caseInfo) {
    commit('SET_CASE_INFO', caseInfo);
  },
  
  resetCaseInfo({ commit }) {
    commit('RESET_CASE_INFO');
  },
  
  // UI状态相关
  updateStoreRightWidth({ commit }, width) {
    commit('SET_STORERIGHTWIDTH', width);
  },
  
  toggleSidebar({ commit, state }) {
    commit('SET_SIDEBAR_COLLAPSED', !state.ui.sidebarCollapsed);
  },
  
  setTheme({ commit }, theme) {
    commit('SET_THEME', theme);
  },
  
  setLanguage({ commit }, language) {
    commit('SET_LANGUAGE', language);
  },
  
  showLoading({ commit }) {
    commit('SET_LOADING', true);
  },
  
  hideLoading({ commit }) {
    commit('SET_LOADING', false);
  },
  
  showNotification({ commit }, { message, type = 'info', duration = 3000 }) {
    commit('SET_NOTIFICATION', {
      show: true,
      message,
      type,
      duration
    });
    
    // 自动隐藏通知
    setTimeout(() => {
      commit('HIDE_NOTIFICATION');
    }, duration);
  },
  
  hideNotification({ commit }) {
    commit('HIDE_NOTIFICATION');
  },
  
  // 审查状态相关
  setCurrentStep({ commit }, step) {
    commit('SET_CURRENT_STEP', step);
  },
  
  setTotalSteps({ commit }, steps) {
    commit('SET_TOTAL_STEPS', steps);
  },
  
  updateReviewData({ commit }, data) {
    commit('SET_REVIEW_DATA', data);
  },
  
  setSelectedItems({ commit }, items) {
    commit('SET_SELECTED_ITEMS', items);
  },
  
  addSelectedItem({ commit }, item) {
    commit('ADD_SELECTED_ITEM', item);
  },
  
  removeSelectedItem({ commit }, itemId) {
    commit('REMOVE_SELECTED_ITEM', itemId);
  },
  
  setReviewHistory({ commit }, history) {
    commit('SET_REVIEW_HISTORY', history);
  },
  
  addReviewHistory({ commit }, record) {
    commit('ADD_REVIEW_HISTORY', record);
  },
  
  setAutoSave({ commit }, autoSave) {
    commit('SET_AUTO_SAVE', autoSave);
  },
  
  updateLastSaved({ commit }) {
    commit('SET_LAST_SAVED', new Date().toISOString());
  },
  
  // 自动保存审查数据
  async autoSaveReviewData({ commit, state }) {
    if (state.review.autoSave && state.review.reviewData) {
      try {
        // 这里可以实现自动保存逻辑
        console.log('自动保存审查数据:', state.review.reviewData);
        commit('UPDATE_LAST_SAVED');
      } catch (error) {
        console.error('自动保存失败:', error);
      }
    }
  },
  
  // 系统配置相关
  updateSystemConfig({ commit }, config) {
    commit('SET_SYSTEM_CONFIG', config);
  },
  
  // 重置所有状态
  resetAllState({ commit }) {
    commit('RESET_ALL_STATE');
  },
  
  // 初始化应用状态
  initializeApp({ commit, dispatch }) {
    console.log('初始化应用状态');
    
    // 从localStorage或sessionStorage恢复状态
    // 这部分由vuex-persistedstate插件自动处理
    
    // 初始化系统配置
    dispatch('updateSystemConfig', {
      apiBaseUrl: process.env.VUE_APP_API_BASE_URL || '/api',
      environment: process.env.NODE_ENV || 'development'
    });
  }
};

// ========== Getters ==========

const getters = {
  // 人员信息相关
  personInfo: state => state.personInfo,
  isAuthenticated: state => !!state.personInfo.id,
  userPermissions: state => state.personInfo.permissions,
  userRoles: state => state.personInfo.roles,
  
  // 案件信息相关
  caseInfo: state => state.caseInfo,
  hasActiveCase: state => !!state.caseInfo.id,
  
  // UI状态相关
  storeRightWidth: state => state.ui.storeRightWidth,
  sidebarCollapsed: state => state.ui.sidebarCollapsed,
  currentTheme: state => state.ui.theme,
  currentLanguage: state => state.ui.language,
  isLoading: state => state.ui.loading,
  notification: state => state.ui.notification,
  
  // 审查状态相关
  currentStep: state => state.review.currentStep,
  totalSteps: state => state.review.totalSteps,
  reviewProgress: state => {
    if (state.review.totalSteps === 0) return 0;
    return Math.round((state.review.currentStep / state.review.totalSteps) * 100);
  },
  reviewData: state => state.review.reviewData,
  selectedItems: state => state.review.selectedItems,
  reviewHistory: state => state.review.reviewHistory,
  isAutoSaveEnabled: state => state.review.autoSave,
  lastSavedTime: state => state.review.lastSaved,
  
  // 系统配置相关
  systemConfig: state => state.system,
  apiBaseUrl: state => state.system.apiBaseUrl,
  maxFileSize: state => state.system.maxFileSize,
  supportedFileTypes: state => state.system.supportedFileTypes,
  
  // 工具方法
  formatDate: () => (date, format = 'YYYY-MM-DD') => {
    // 日期格式化工具方法
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    
    switch (format) {
      case 'YYYY-MM-DD':
        return `${year}-${month}-${day}`;
      case 'YYYY-MM-DD HH:mm:ss':
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      case 'YYYY年MM月DD日':
        return `${year}年${month}月${day}日`;
      case 'YYYY年MM月DD日 HH时mm分ss秒':
        return `${year}年${month}月${day}日 ${hours}时${minutes}分${seconds}秒`;
      default:
        return date;
    }
  }
};

// ========== Store 配置 ==========

// 持久化配置
const persistConfig = {
  key: 'zjsc-vuex',
  storage: window.sessionStorage,
  paths: [
    'personInfo',
    'caseInfo',
    'ui.storeRightWidth',
    'ui.sidebarCollapsed',
    'ui.theme',
    'ui.language',
    'review.currentStep',
    'review.totalSteps',
    'review.reviewData',
    'review.selectedItems',
    'review.autoSave',
    'system'
  ]
};

// 创建Store实例
const store = new Vuex.Store({
  state: defaultState,
  mutations,
  actions,
  getters,
  plugins: [createPersistedState(persistConfig)],
  strict: process.env.NODE_ENV !== 'production'
});

// ========== 状态订阅 ==========

// 订阅状态变化
store.subscribe((mutation, state) => {
  console.log('Vuex状态变化:', mutation.type, mutation.payload);
  
  // 可以在这里添加状态变化的处理逻辑
  switch (mutation.type) {
    case 'SET_PERSON_INFO':
      // 人员信息变化处理
      break;
    case 'SET_CASE_INFO':
      // 案件信息变化处理
      break;
    case 'SET_STORERIGHTWIDTH':
      // 右侧面板宽度变化处理
      break;
  }
});

// 导出Store实例
export default store;

// 导出工具方法
export { defaultState, mutations, actions, getters };