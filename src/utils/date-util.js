/**
 * ZJSC 证据审查系统 - 日期工具类
 * 
 * 功能: 提供日期格式化、计算、验证等工具方法
 * 
 * 主要功能：
 * - 日期格式化
 * - 日期计算
 * - 日期验证
 * - 时间差计算
 */

/**
 * 日期工具类
 */
export class DateUtil {
  /**
   * 格式化日期
   * @param {Date|string|number} date - 日期对象、字符串或时间戳
   * @param {string} format - 格式字符串
   * @returns {string} 格式化后的日期字符串
   * 
   * @example
   * DateUtil.format(new Date(), 'YYYY-MM-DD') // '2025-01-04'
   * DateUtil.format(new Date(), 'YYYY年MM月DD日') // '2025年01月04日'
   */
  static format(date, format = 'YYYY-MM-DD') {
    if (!date) return ''
    
    const d = new Date(date)
    if (isNaN(d.getTime())) return ''
    
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')
    const milliseconds = String(d.getMilliseconds()).padStart(3, '0')
    
    const formatMap = {
      'YYYY': year,
      'MM': month,
      'DD': day,
      'HH': hours,
      'mm': minutes,
      'ss': seconds,
      'SSS': milliseconds,
    }
    
    let result = format
    Object.keys(formatMap).forEach(key => {
      result = result.replace(new RegExp(key, 'g'), formatMap[key])
    })
    
    return result
  }

  /**
   * 获取当前时间戳
   * @returns {number} 时间戳
   */
  static now() {
    return Date.now()
  }

  /**
   * 获取今天的开始时间
   * @returns {Date} 今天的开始时间
   */
  static startOfToday() {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return today
  }

  /**
   * 获取今天的结束时间
   * @returns {Date} 今天的结束时间
   */
  static endOfToday() {
    const today = new Date()
    today.setHours(23, 59, 59, 999)
    return today
  }

  /**
   * 计算两个日期之间的天数差
   * @param {Date|string} startDate - 开始日期
   * @param {Date|string} endDate - 结束日期
   * @returns {number} 天数差
   */
  static daysBetween(startDate, endDate) {
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return 0
    }
    
    const timeDiff = end.getTime() - start.getTime()
    return Math.ceil(timeDiff / (1000 * 3600 * 24))
  }

  /**
   * 添加天数
   * @param {Date|string} date - 基础日期
   * @param {number} days - 要添加的天数
   * @returns {Date} 新日期
   */
  static addDays(date, days) {
    const result = new Date(date)
    result.setDate(result.getDate() + days)
    return result
  }

  /**
   * 添加月份
   * @param {Date|string} date - 基础日期
   * @param {number} months - 要添加的月份
   * @returns {Date} 新日期
   */
  static addMonths(date, months) {
    const result = new Date(date)
    result.setMonth(result.getMonth() + months)
    return result
  }

  /**
   * 添加年份
   * @param {Date|string} date - 基础日期
   * @param {number} years - 要添加的年份
   * @returns {Date} 新日期
   */
  static addYears(date, years) {
    const result = new Date(date)
    result.setFullYear(result.getFullYear() + years)
    return result
  }

  /**
   * 判断是否为有效日期
   * @param {any} date - 要验证的日期
   * @returns {boolean} 是否为有效日期
   */
  static isValidDate(date) {
    const d = new Date(date)
    return !isNaN(d.getTime())
  }

  /**
   * 判断是否为今天
   * @param {Date|string} date - 要判断的日期
   * @returns {boolean} 是否为今天
   */
  static isToday(date) {
    const today = new Date()
    const targetDate = new Date(date)
    
    return (
      today.getFullYear() === targetDate.getFullYear() &&
      today.getMonth() === targetDate.getMonth() &&
      today.getDate() === targetDate.getDate()
    )
  }

  /**
   * 判断是否为昨天
   * @param {Date|string} date - 要判断的日期
   * @returns {boolean} 是否为昨天
   */
  static isYesterday(date) {
    const yesterday = this.addDays(new Date(), -1)
    const targetDate = new Date(date)
    
    return (
      yesterday.getFullYear() === targetDate.getFullYear() &&
      yesterday.getMonth() === targetDate.getMonth() &&
      yesterday.getDate() === targetDate.getDate()
    )
  }

  /**
   * 获取相对时间描述
   * @param {Date|string} date - 目标日期
   * @returns {string} 相对时间描述
   * 
   * @example
   * DateUtil.getRelativeTime(new Date()) // '刚刚'
   * DateUtil.getRelativeTime(Date.now() - 60000) // '1分钟前'
   */
  static getRelativeTime(date) {
    const now = new Date()
    const target = new Date(date)
    const diff = now.getTime() - target.getTime()
    
    if (diff < 0) {
      return '未来时间'
    }
    
    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    const months = Math.floor(days / 30)
    const years = Math.floor(days / 365)
    
    if (seconds < 60) {
      return '刚刚'
    } else if (minutes < 60) {
      return `${minutes}分钟前`
    } else if (hours < 24) {
      return `${hours}小时前`
    } else if (days < 30) {
      return `${days}天前`
    } else if (months < 12) {
      return `${months}个月前`
    } else {
      return `${years}年前`
    }
  }

  /**
   * 获取月份的天数
   * @param {number} year - 年份
   * @param {number} month - 月份 (1-12)
   * @returns {number} 该月的天数
   */
  static getDaysInMonth(year, month) {
    return new Date(year, month, 0).getDate()
  }

  /**
   * 判断是否为闰年
   * @param {number} year - 年份
   * @returns {boolean} 是否为闰年
   */
  static isLeapYear(year) {
    return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0)
  }

  /**
   * 获取星期几
   * @param {Date|string} date - 日期
   * @returns {string} 星期几的中文描述
   */
  static getWeekday(date) {
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
    const d = new Date(date)
    return weekdays[d.getDay()]
  }

  /**
   * 解析日期字符串
   * @param {string} dateString - 日期字符串
   * @param {string} format - 格式字符串
   * @returns {Date|null} 解析后的日期对象
   */
  static parse(dateString, format = 'YYYY-MM-DD') {
    if (!dateString) return null
    
    try {
      // 简单的日期解析实现
      if (format === 'YYYY-MM-DD') {
        const parts = dateString.split('-')
        if (parts.length === 3) {
          const year = parseInt(parts[0])
          const month = parseInt(parts[1]) - 1 // 月份从0开始
          const day = parseInt(parts[2])
          return new Date(year, month, day)
        }
      }
      
      // 其他格式可以扩展
      return new Date(dateString)
    } catch (error) {
      console.error('日期解析失败:', error)
      return null
    }
  }
}

// 默认导出
export default DateUtil
