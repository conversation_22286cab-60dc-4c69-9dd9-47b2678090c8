/**
 * ZJSC 证据审查系统 - WPS 工具类
 * 
 * 功能: 提供 WPS 文档处理相关的工具方法
 * 
 * 主要功能：
 * - WPS 文档打开和编辑
 * - 文档格式转换
 * - 文档内容提取
 * - 文档模板处理
 */

/**
 * WPS 工具类
 */
export class WpsUtil {
  /**
   * 检查是否安装了 WPS
   * @returns {boolean} 是否安装了 WPS
   */
  static isWpsInstalled() {
    try {
      // 检查是否存在 WPS 相关的 ActiveX 控件或插件
      if (window.ActiveXObject) {
        try {
          new ActiveXObject('Kwps.Application')
          return true
        } catch (e) {
          return false
        }
      }
      
      // 检查是否存在 WPS 的 JavaScript API
      return !!(window.WPS || window.wps)
    } catch (error) {
      console.error('检查 WPS 安装状态失败:', error)
      return false
    }
  }

  /**
   * 打开 WPS 文档
   * @param {string} filePath - 文件路径
   * @param {Object} options - 打开选项
   * @returns {Promise<boolean>} 是否成功打开
   */
  static async openDocument(filePath, options = {}) {
    try {
      if (!this.isWpsInstalled()) {
        throw new Error('未检测到 WPS 软件，请先安装 WPS')
      }

      const defaultOptions = {
        readOnly: false,
        visible: true,
        enableEdit: true,
        ...options
      }

      // 使用 WPS API 打开文档
      if (window.WPS) {
        const app = new window.WPS.Application()
        const doc = app.Documents.Open(filePath, defaultOptions)
        return !!doc
      }

      // 备用方案：通过 URL 协议打开
      const wpsUrl = `wps://openoffice.org/open?url=${encodeURIComponent(filePath)}`
      window.open(wpsUrl)
      return true
    } catch (error) {
      console.error('打开 WPS 文档失败:', error)
      throw error
    }
  }

  /**
   * 创建新的 WPS 文档
   * @param {string} templatePath - 模板文件路径（可选）
   * @param {Object} options - 创建选项
   * @returns {Promise<Object>} 文档对象
   */
  static async createDocument(templatePath = null, options = {}) {
    try {
      if (!this.isWpsInstalled()) {
        throw new Error('未检测到 WPS 软件，请先安装 WPS')
      }

      const defaultOptions = {
        visible: true,
        enableEdit: true,
        ...options
      }

      if (window.WPS) {
        const app = new window.WPS.Application()
        let doc

        if (templatePath) {
          // 基于模板创建文档
          doc = app.Documents.Add(templatePath)
        } else {
          // 创建空白文档
          doc = app.Documents.Add()
        }

        if (defaultOptions.visible) {
          app.Visible = true
        }

        return doc
      }

      throw new Error('WPS API 不可用')
    } catch (error) {
      console.error('创建 WPS 文档失败:', error)
      throw error
    }
  }

  /**
   * 保存文档
   * @param {Object} document - WPS 文档对象
   * @param {string} filePath - 保存路径
   * @param {string} format - 保存格式
   * @returns {Promise<boolean>} 是否保存成功
   */
  static async saveDocument(document, filePath, format = 'docx') {
    try {
      if (!document) {
        throw new Error('文档对象不能为空')
      }

      // 根据格式设置保存类型
      const formatMap = {
        'doc': 0,
        'docx': 16,
        'pdf': 17,
        'txt': 2,
        'rtf': 6
      }

      const saveFormat = formatMap[format.toLowerCase()] || 16

      if (filePath) {
        document.SaveAs2(filePath, saveFormat)
      } else {
        document.Save()
      }

      return true
    } catch (error) {
      console.error('保存 WPS 文档失败:', error)
      throw error
    }
  }

  /**
   * 关闭文档
   * @param {Object} document - WPS 文档对象
   * @param {boolean} saveChanges - 是否保存更改
   * @returns {Promise<boolean>} 是否关闭成功
   */
  static async closeDocument(document, saveChanges = true) {
    try {
      if (!document) {
        return true
      }

      document.Close(saveChanges)
      return true
    } catch (error) {
      console.error('关闭 WPS 文档失败:', error)
      throw error
    }
  }

  /**
   * 获取文档内容
   * @param {Object} document - WPS 文档对象
   * @returns {Promise<string>} 文档文本内容
   */
  static async getDocumentContent(document) {
    try {
      if (!document) {
        throw new Error('文档对象不能为空')
      }

      return document.Content.Text || ''
    } catch (error) {
      console.error('获取文档内容失败:', error)
      throw error
    }
  }

  /**
   * 设置文档内容
   * @param {Object} document - WPS 文档对象
   * @param {string} content - 要设置的内容
   * @returns {Promise<boolean>} 是否设置成功
   */
  static async setDocumentContent(document, content) {
    try {
      if (!document) {
        throw new Error('文档对象不能为空')
      }

      document.Content.Text = content
      return true
    } catch (error) {
      console.error('设置文档内容失败:', error)
      throw error
    }
  }

  /**
   * 在文档中查找文本
   * @param {Object} document - WPS 文档对象
   * @param {string} searchText - 要查找的文本
   * @param {Object} options - 查找选项
   * @returns {Promise<Array>} 查找结果数组
   */
  static async findText(document, searchText, options = {}) {
    try {
      if (!document || !searchText) {
        return []
      }

      const defaultOptions = {
        matchCase: false,
        matchWholeWord: false,
        ...options
      }

      const range = document.Content
      const find = range.Find

      find.ClearFormatting()
      find.Text = searchText
      find.MatchCase = defaultOptions.matchCase
      find.MatchWholeWord = defaultOptions.matchWholeWord

      const results = []
      while (find.Execute()) {
        results.push({
          text: range.Text,
          start: range.Start,
          end: range.End
        })
      }

      return results
    } catch (error) {
      console.error('查找文本失败:', error)
      throw error
    }
  }

  /**
   * 替换文档中的文本
   * @param {Object} document - WPS 文档对象
   * @param {string} searchText - 要查找的文本
   * @param {string} replaceText - 替换的文本
   * @param {Object} options - 替换选项
   * @returns {Promise<number>} 替换的次数
   */
  static async replaceText(document, searchText, replaceText, options = {}) {
    try {
      if (!document || !searchText) {
        return 0
      }

      const defaultOptions = {
        matchCase: false,
        matchWholeWord: false,
        replaceAll: true,
        ...options
      }

      const range = document.Content
      const find = range.Find

      find.ClearFormatting()
      find.Text = searchText
      find.Replacement.Text = replaceText
      find.MatchCase = defaultOptions.matchCase
      find.MatchWholeWord = defaultOptions.matchWholeWord

      if (defaultOptions.replaceAll) {
        return find.Execute(15, false, 0, false, false, false, true, 1, true, replaceText, 2)
      } else {
        return find.Execute() ? 1 : 0
      }
    } catch (error) {
      console.error('替换文本失败:', error)
      throw error
    }
  }

  /**
   * 转换文档格式
   * @param {string} inputPath - 输入文件路径
   * @param {string} outputPath - 输出文件路径
   * @param {string} outputFormat - 输出格式
   * @returns {Promise<boolean>} 是否转换成功
   */
  static async convertDocument(inputPath, outputPath, outputFormat) {
    try {
      if (!this.isWpsInstalled()) {
        throw new Error('未检测到 WPS 软件，请先安装 WPS')
      }

      const document = await this.openDocument(inputPath, { visible: false })
      await this.saveDocument(document, outputPath, outputFormat)
      await this.closeDocument(document, false)

      return true
    } catch (error) {
      console.error('转换文档格式失败:', error)
      throw error
    }
  }

  /**
   * 获取 WPS 版本信息
   * @returns {Promise<Object>} 版本信息
   */
  static async getWpsVersion() {
    try {
      if (!this.isWpsInstalled()) {
        return null
      }

      if (window.WPS) {
        const app = new window.WPS.Application()
        return {
          name: app.Name,
          version: app.Version,
          build: app.Build
        }
      }

      return null
    } catch (error) {
      console.error('获取 WPS 版本信息失败:', error)
      return null
    }
  }

  /**
   * 检查文档是否被修改
   * @param {Object} document - WPS 文档对象
   * @returns {boolean} 是否被修改
   */
  static isDocumentModified(document) {
    try {
      if (!document) {
        return false
      }

      return document.Saved === false
    } catch (error) {
      console.error('检查文档修改状态失败:', error)
      return false
    }
  }
}

// 默认导出
export default WpsUtil
