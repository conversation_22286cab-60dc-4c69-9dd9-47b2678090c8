/**
 * ZJSC 证据审查系统 - 工具类统一入口
 * 
 * 功能: 统一导出所有工具类，提供便捷的工具访问接口
 * 
 * 包含的工具类：
 * - 树形数据处理工具 (Module f382)
 * - 微前端状态管理工具 (Module abea)
 * - 日期工具类
 * - WPS 文档处理工具
 * - 加密工具类 (Module a178)
 */

// 导入所有工具类
import TreeUtil from './tree-util'
import MicroStateManager from './micro-state'
import DateUtil from './date-util'
import WpsUtil from './wps-util'
import CryptoUtil from './crypto-util'

/**
 * 统一的工具对象
 */
export const utils = {
  // 树形数据处理工具
  TreeUtil,
  
  // 微前端状态管理工具
  MicroStateManager,
  
  // 日期工具类
  DateUtil,
  
  // WPS 文档处理工具
  WpsUtil,
  
  // 加密工具类
  CryptoUtil
}

/**
 * 工具类管理器
 * 提供统一的工具类访问和管理接口
 */
export class UtilsManager {
  constructor() {
    this.utils = utils
  }

  /**
   * 获取树形数据处理工具
   * @returns {Object} TreeUtil 工具类
   */
  getTreeUtil() {
    return this.utils.TreeUtil
  }

  /**
   * 获取微前端状态管理工具
   * @returns {Object} MicroStateManager 实例
   */
  getMicroStateManager() {
    return this.utils.MicroStateManager
  }

  /**
   * 获取日期工具类
   * @returns {Object} DateUtil 工具类
   */
  getDateUtil() {
    return this.utils.DateUtil
  }

  /**
   * 获取WPS工具类
   * @returns {Object} WpsUtil 工具类
   */
  getWpsUtil() {
    return this.utils.WpsUtil
  }

  /**
   * 获取加密工具类
   * @returns {Object} CryptoUtil 工具类
   */
  getCryptoUtil() {
    return this.utils.CryptoUtil
  }

  /**
   * 将扁平数组转换为树形结构
   * @param {Array} flatArray - 扁平数组
   * @param {string} idField - ID字段名
   * @param {string} parentIdField - 父ID字段名
   * @returns {Array} 树形结构数组
   */
  arrayToTree(flatArray, idField = 'id', parentIdField = 'pid') {
    return this.utils.TreeUtil.getTreeData(flatArray, idField, parentIdField)
  }

  /**
   * 格式化日期
   * @param {Date|string|number} date - 日期
   * @param {string} format - 格式字符串
   * @returns {string} 格式化后的日期字符串
   */
  formatDate(date, format = 'YYYY-MM-DD') {
    return this.utils.DateUtil.format(date, format)
  }

  /**
   * 加密字符串
   * @param {string} plainText - 明文
   * @param {string} key - 密钥（可选）
   * @returns {string} 密文
   */
  encrypt(plainText, key = null) {
    return this.utils.CryptoUtil.encrypt(plainText, key)
  }

  /**
   * 解密字符串
   * @param {string} cipherText - 密文
   * @param {string} key - 密钥（可选）
   * @returns {string} 明文
   */
  decrypt(cipherText, key = null) {
    return this.utils.CryptoUtil.decrypt(cipherText, key)
  }

  /**
   * 检查WPS是否已安装
   * @returns {boolean} 是否已安装WPS
   */
  isWpsInstalled() {
    return this.utils.WpsUtil.isWpsInstalled()
  }

  /**
   * 获取相对时间描述
   * @param {Date|string} date - 目标日期
   * @returns {string} 相对时间描述
   */
  getRelativeTime(date) {
    return this.utils.DateUtil.getRelativeTime(date)
  }

  /**
   * 计算两个日期之间的天数差
   * @param {Date|string} startDate - 开始日期
   * @param {Date|string} endDate - 结束日期
   * @returns {number} 天数差
   */
  daysBetween(startDate, endDate) {
    return this.utils.DateUtil.daysBetween(startDate, endDate)
  }

  /**
   * 生成随机加密密钥
   * @param {number} length - 密钥长度
   * @returns {string} 随机密钥
   */
  generateRandomKey(length = 64) {
    return this.utils.CryptoUtil.generateRandomKey(length)
  }

  /**
   * 计算字符串哈希值
   * @param {string} input - 输入字符串
   * @returns {string} 哈希值
   */
  hash(input) {
    return this.utils.CryptoUtil.hash(input)
  }

  /**
   * 获取工具类统计信息
   * @returns {Object} 工具类统计信息
   */
  getUtilsStats() {
    return {
      availableUtils: Object.keys(this.utils).length,
      utilsList: Object.keys(this.utils),
      microStateManagerStatus: this.utils.MicroStateManager.default?.hasValidActions() || false,
      wpsInstalled: this.utils.WpsUtil.isWpsInstalled(),
      currentTime: this.utils.DateUtil.now()
    }
  }
}

// 创建全局工具管理器实例
export const utilsManager = new UtilsManager()

/**
 * 便捷的工具访问函数
 */

// 树形数据处理
export const arrayToTree = (flatArray, idField, parentIdField) => 
  utilsManager.arrayToTree(flatArray, idField, parentIdField)

// 日期处理
export const formatDate = (date, format) => utilsManager.formatDate(date, format)
export const getRelativeTime = (date) => utilsManager.getRelativeTime(date)
export const daysBetween = (startDate, endDate) => utilsManager.daysBetween(startDate, endDate)

// 加密处理
export const encrypt = (plainText, key) => utilsManager.encrypt(plainText, key)
export const decrypt = (cipherText, key) => utilsManager.decrypt(cipherText, key)
export const generateRandomKey = (length) => utilsManager.generateRandomKey(length)
export const hash = (input) => utilsManager.hash(input)

// WPS 处理
export const isWpsInstalled = () => utilsManager.isWpsInstalled()

// 重新导出各个工具类（保持向后兼容）
export { default as TreeUtil } from './tree-util'
export { default as MicroStateManager } from './micro-state'
export { default as DateUtil } from './date-util'
export { default as WpsUtil } from './wps-util'
export { default as CryptoUtil } from './crypto-util'

// 默认导出工具管理器
export default utilsManager
