/**
 * ZJSC 证据审查系统 - 微前端全局状态管理工具类
 * 
 * Module ID: abea
 * 功能: 微前端应用的全局状态管理，用于主应用与子应用之间的通信
 * 
 * 主要功能：
 * - 管理 qiankun 微前端的全局状态
 * - 提供状态变化监听和状态设置功能
 * - 作为主应用与子应用之间的通信桥梁
 */

/**
 * 空操作函数，用于默认的 action 占位
 * @param {...any} args - 任意参数
 */
function emptyAction(...args) {
  console.warn('警告：提示当前使用的是空 Action')
}

/**
 * 微前端全局状态管理类
 * 用于管理 qiankun 微前端框架的全局状态通信
 */
class MicroFrontendStateManager {
  /**
   * 构造函数
   * 初始化默认的空 actions
   */
  constructor() {
    /**
     * 全局状态管理 actions
     * @type {Object}
     */
    this.actions = {
      /**
       * 监听全局状态变化的方法
       * @type {Function}
       */
      onGlobalStateChange: emptyAction,
      
      /**
       * 设置全局状态的方法
       * @type {Function}
       */
      setGlobalState: emptyAction,
    }
  }

  /**
   * 设置全局状态管理 actions
   * 通常在微前端应用挂载时由主应用传入
   * @param {Object} actions - 全局状态管理方法对象
   * @param {Function} actions.onGlobalStateChange - 监听全局状态变化
   * @param {Function} actions.setGlobalState - 设置全局状态
   */
  setActions(actions) {
    if (actions && typeof actions === 'object') {
      this.actions = actions
    } else {
      console.warn('MicroFrontendStateManager: 传入的 actions 必须是对象')
    }
  }

  /**
   * 监听全局状态变化
   * @param {...any} args - 传递给监听函数的参数
   * @returns {any} 监听函数的返回值
   * 
   * @example
   * // 监听全局状态变化
   * stateManager.onGlobalStateChange((newState, prevState) => {
   *   console.log('状态变化:', newState, prevState)
   * })
   */
  onGlobalStateChange(...args) {
    return this.actions.onGlobalStateChange(...args)
  }

  /**
   * 设置全局状态
   * @param {...any} args - 传递给设置函数的参数
   * @returns {any} 设置函数的返回值
   * 
   * @example
   * // 设置全局状态
   * stateManager.setGlobalState({
   *   user: { name: 'John', id: 123 },
   *   theme: 'dark'
   * })
   */
  setGlobalState(...args) {
    return this.actions.setGlobalState(...args)
  }

  /**
   * 检查是否已设置有效的 actions
   * @returns {boolean} 是否有有效的 actions
   */
  hasValidActions() {
    return (
      this.actions.onGlobalStateChange !== emptyAction &&
      this.actions.setGlobalState !== emptyAction
    )
  }

  /**
   * 重置 actions 为默认空操作
   */
  resetActions() {
    this.actions = {
      onGlobalStateChange: emptyAction,
      setGlobalState: emptyAction,
    }
  }

  /**
   * 获取当前 actions 的状态信息
   * @returns {Object} actions 状态信息
   */
  getActionsInfo() {
    return {
      hasValidActions: this.hasValidActions(),
      actionsType: {
        onGlobalStateChange: typeof this.actions.onGlobalStateChange,
        setGlobalState: typeof this.actions.setGlobalState,
      }
    }
  }
}

// 创建全局实例
const microFrontendStateManager = new MicroFrontendStateManager()

// 导出实例
export default microFrontendStateManager

// 也可以导出类，供其他地方创建新实例
export { MicroFrontendStateManager }
