/**
 * ZJSC 证据审查系统 - 加密工具类
 * 
 * Module ID: a178
 * 功能: 提供数据加密和解密功能，用于敏感信息的安全处理
 * 
 * 主要功能：
 * - 字符串加密
 * - 字符串解密
 * - 基于固定密钥的对称加密算法
 */

/**
 * 加密工具类
 * 使用自定义的对称加密算法进行数据加密和解密
 */
export class CryptoUtil {
  /**
   * 默认加密密钥
   * @private
   */
  static #DEFAULT_KEY = "ae8b516cbffde62a74b726c82eb6748ad41d251480d84ce2e96ebf57ebaa8b22"

  /**
   * 加密字符串
   * @param {string} plainText - 要加密的明文
   * @param {string} key - 加密密钥（可选，使用默认密钥）
   * @returns {string} 加密后的密文
   * 
   * @example
   * const encrypted = CryptoUtil.encrypt('Hello World')
   * console.log(encrypted) // 返回加密后的十六进制字符串
   */
  static encrypt(plainText, key = null) {
    if (plainText === "") return ""
    
    // 对输入进行URL编码
    plainText = escape(plainText)
    
    // 使用提供的密钥或默认密钥
    let encryptionKey = key || this.#DEFAULT_KEY
    encryptionKey = escape(encryptionKey)
    
    // 验证密钥
    if (encryptionKey === null || encryptionKey.length <= 0) {
      console.error("加密密钥不能为空")
      return null
    }

    // 生成密钥哈希
    let keyHash = ""
    for (let i = 0; i < encryptionKey.length; i++) {
      keyHash += encryptionKey.charCodeAt(i).toString()
    }

    // 计算哈希参数
    const hashLength = Math.floor(keyHash.length / 5)
    const hashValue = parseInt(
      keyHash.charAt(hashLength) +
      keyHash.charAt(2 * hashLength) +
      keyHash.charAt(3 * hashLength) +
      keyHash.charAt(4 * hashLength) +
      keyHash.charAt(5 * hashLength)
    )
    
    const keyMidpoint = Math.ceil(encryptionKey.length / 2)
    const maxValue = Math.pow(2, 31) - 1

    // 验证哈希值
    if (hashValue < 2) {
      console.error("算法无法找到合适的哈希值，请选择不同的密码")
      return null
    }

    // 生成随机盐值
    let salt = Math.round(Math.random() * 1e9) % 1e8
    keyHash += salt

    // 压缩哈希值
    while (keyHash.length > 10) {
      keyHash = (
        parseInt(keyHash.substring(0, 10)) + 
        parseInt(keyHash.substring(10, keyHash.length))
      ).toString()
    }

    keyHash = (hashValue * keyHash + keyMidpoint) % maxValue

    // 执行加密
    let encryptedHex = ""
    for (let i = 0; i < plainText.length; i++) {
      const charCode = parseInt(plainText.charCodeAt(i) ^ Math.floor((keyHash / maxValue) * 255))
      encryptedHex += charCode < 16 ? "0" + charCode.toString(16) : charCode.toString(16)
      keyHash = (hashValue * keyHash + keyMidpoint) % maxValue
    }

    // 添加盐值到结果
    salt = salt.toString(16)
    while (salt.length < 8) {
      salt = "0" + salt
    }

    return encryptedHex + salt
  }

  /**
   * 解密字符串
   * @param {string} cipherText - 要解密的密文
   * @param {string} key - 解密密钥（可选，使用默认密钥）
   * @returns {string} 解密后的明文
   * 
   * @example
   * const decrypted = CryptoUtil.decrypt(encryptedText)
   * console.log(decrypted) // 返回原始明文
   */
  static decrypt(cipherText, key = null) {
    if (cipherText === "") return ""

    // 使用提供的密钥或默认密钥
    let decryptionKey = key || this.#DEFAULT_KEY
    decryptionKey = escape(decryptionKey)

    // 验证密文长度
    if (cipherText === null || cipherText.length < 8) {
      console.error("无法从加密消息中提取盐值，消息长度太短，无法解密")
      return ""
    }

    // 验证密钥
    if (decryptionKey === null || decryptionKey.length <= 0) {
      console.error("解密密钥不能为空")
      return ""
    }

    // 生成密钥哈希
    let keyHash = ""
    for (let i = 0; i < decryptionKey.length; i++) {
      keyHash += decryptionKey.charCodeAt(i).toString()
    }

    // 计算哈希参数
    const hashLength = Math.floor(keyHash.length / 5)
    const hashValue = parseInt(
      keyHash.charAt(hashLength) +
      keyHash.charAt(2 * hashLength) +
      keyHash.charAt(3 * hashLength) +
      keyHash.charAt(4 * hashLength) +
      keyHash.charAt(5 * hashLength)
    )
    
    const keyMidpoint = Math.round(decryptionKey.length / 2)
    const maxValue = Math.pow(2, 31) - 1

    // 提取盐值
    const salt = parseInt(cipherText.substring(cipherText.length - 8, cipherText.length), 16)
    cipherText = cipherText.substring(0, cipherText.length - 8)

    // 重建哈希
    keyHash += salt
    while (keyHash.length > 10) {
      keyHash = (
        parseInt(keyHash.substring(0, 10)) + 
        parseInt(keyHash.substring(10, keyHash.length))
      ).toString()
    }

    keyHash = (hashValue * keyHash + keyMidpoint) % maxValue

    // 执行解密
    let decryptedText = ""
    for (let i = 0; i < cipherText.length; i += 2) {
      const hexPair = cipherText.substring(i, i + 2)
      const charCode = parseInt(
        parseInt(hexPair, 16) ^ Math.floor((keyHash / maxValue) * 255)
      )
      decryptedText += String.fromCharCode(charCode)
      keyHash = (hashValue * keyHash + keyMidpoint) % maxValue
    }

    // 返回URL解码后的结果
    return unescape(decryptedText)
  }

  /**
   * 生成随机密钥
   * @param {number} length - 密钥长度
   * @returns {string} 随机密钥
   */
  static generateRandomKey(length = 64) {
    const chars = "0123456789abcdef"
    let result = ""
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  /**
   * 验证密文格式
   * @param {string} cipherText - 密文
   * @returns {boolean} 是否为有效的密文格式
   */
  static isValidCipherText(cipherText) {
    if (typeof cipherText !== 'string' || cipherText.length < 8) {
      return false
    }
    
    // 检查是否为有效的十六进制字符串
    const hexPattern = /^[0-9a-fA-F]+$/
    return hexPattern.test(cipherText)
  }

  /**
   * 计算字符串哈希值
   * @param {string} input - 输入字符串
   * @returns {string} 哈希值
   */
  static hash(input) {
    let hash = 0
    if (input.length === 0) return hash.toString()
    
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(16)
  }

  /**
   * 安全比较两个字符串
   * @param {string} a - 字符串A
   * @param {string} b - 字符串B
   * @returns {boolean} 是否相等
   */
  static secureCompare(a, b) {
    if (a.length !== b.length) {
      return false
    }
    
    let result = 0
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i)
    }
    
    return result === 0
  }

  /**
   * 编码Base64
   * @param {string} input - 输入字符串
   * @returns {string} Base64编码结果
   */
  static encodeBase64(input) {
    try {
      return btoa(unescape(encodeURIComponent(input)))
    } catch (error) {
      console.error('Base64编码失败:', error)
      return ''
    }
  }

  /**
   * 解码Base64
   * @param {string} input - Base64字符串
   * @returns {string} 解码结果
   */
  static decodeBase64(input) {
    try {
      return decodeURIComponent(escape(atob(input)))
    } catch (error) {
      console.error('Base64解码失败:', error)
      return ''
    }
  }
}

// 便捷的静态方法导出
export const encrypt = CryptoUtil.encrypt.bind(CryptoUtil)
export const decrypt = CryptoUtil.decrypt.bind(CryptoUtil)
export const generateRandomKey = CryptoUtil.generateRandomKey.bind(CryptoUtil)
export const hash = CryptoUtil.hash.bind(CryptoUtil)

// 默认导出
export default CryptoUtil
