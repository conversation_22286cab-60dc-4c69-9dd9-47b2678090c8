<template>
  <div class="App">
    <router-view />
  </div>
</template>

<script>
/**
 * ZJSC 证据审查系统 - Vue根组件
 * 
 * Module ID: 56d7 (部分)
 * 功能: 应用根组件，包含路由视图容器
 * 
 * 这是Vue应用的根组件，所有页面组件都通过router-view渲染
 */
export default {
  name: 'App',
  data() {
    return {};
  },
  mounted() {
    // 应用挂载后的初始化逻辑
    console.log('ZJSC 应用已启动');
  },
  methods: {}
};
</script>

<style scoped>
.App {
  width: 100%;
  height: 100%;
}
</style>