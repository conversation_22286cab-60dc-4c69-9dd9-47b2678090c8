# ZJSC 证据审查系统 - 源代码结构

## 📁 项目结构

```
src/
├── api/                    # API 接口层
│   └── http.js            # HTTP 客户端封装 (Module d354)
├── components/            # Vue 组件
├── config/               # 配置数据层
│   ├── evidence-types.js  # 证据分类配置 (Module 0b43)
│   ├── case-archive.js    # 案件档案配置 (Module 2386)
│   ├── review-modes.js    # 审查模式配置 (Module 548b)
│   ├── review-items.js    # 审查项目配置 (Module bf6f)
│   ├── review-process.js  # 审查流程配置 (Module 708c)
│   └── index.js          # 配置统一入口
├── router/               # 路由配置
│   └── index.js          # Vue Router 配置 (Module 88ed)
├── store/                # 状态管理
│   └── index.js          # Vuex Store 配置 (Module f382)
├── utils/                # 工具类
│   ├── tree-util.js      # 树形数据处理工具 (Module f382)
│   ├── micro-state.js    # 微前端状态管理 (Module abea)
│   ├── date-util.js      # 日期工具类
│   ├── wps-util.js       # WPS 文档处理工具
│   ├── crypto-util.js    # 加密工具类 (Module a178)
│   └── index.js          # 工具类统一入口
├── views/                # 页面组件
├── App.vue               # 根组件 (Module 56d7)
├── main.js               # 应用入口 (Module 0)
├── main-app.js           # 主应用入口
├── lifecycle.js          # 微前端生命周期 (Module 8b65)
└── README.md             # 项目说明
```

## 🎯 已还原模块

### 阶段一: 基础架构层 ✅

1. **Module 0** - 应用入口点
   - 文件: `src/main.js`
   - 功能: Webpack 模块系统引导和应用初始化

2. **Module 56d7** - Vue 根组件
   - 文件: `src/App.vue`
   - 功能: 应用根组件，包含路由视图容器

3. **Module 8b65** - 微前端生命周期管理
   - 文件: `src/lifecycle.js`
   - 功能: qiankun 微前端生命周期函数 (bootstrap, mount, unmount, update)

4. **Module 88ed** - Vue Router 路由配置
   - 文件: `src/router/index.js`
   - 功能: 路由表配置和导航守卫

5. **Module f382** - 状态管理
   - 文件: `src/store/index.js` (Vuex Store)
   - 文件: `src/utils/tree-util.js` (树形数据处理工具)
   - 功能: 全局状态管理和数据处理工具

6. **Module d354** - HTTP 客户端封装
   - 文件: `src/api/http.js`
   - 功能: Axios 封装，包含拦截器和错误处理

7. **Module abea** - 微前端状态管理工具
   - 文件: `src/utils/micro-state.js`
   - 功能: qiankun 全局状态管理工具

### 阶段二: 配置数据层 ✅

8. **Module 0b43** - 证据分类数据配置
   - 文件: `src/config/evidence-types.js`
   - 功能: 书证、证人证言、被害人陈述等证据类型配置

9. **Module 2386** - 案件档案数据配置
   - 文件: `src/config/case-archive.js`
   - 功能: 证据材料卷、诉讼文书卷等档案分类配置

10. **Module 548b** - 审查模式配置
    - 文件: `src/config/review-modes.js`
    - 功能: 审查要点、证据分类、犯罪事实、犯罪主体等审查维度

11. **Module bf6f** - 审查项目配置
    - 文件: `src/config/review-items.js`
    - 功能: 具体的审查项目、审查明细、引用文件等配置

12. **Module 708c** - 审查流程配置
    - 文件: `src/config/review-process.js`
    - 功能: 案件基本信息、侦查意见、证据审查、审查认定、审查意见等流程步骤

13. **Module a178** - 加密工具类
    - 文件: `src/utils/crypto-util.js`
    - 功能: 数据加密解密、密钥生成、哈希计算等安全功能

### 扩展工具类

14. **日期工具类**
    - 文件: `src/utils/date-util.js`
    - 功能: 日期格式化、计算、验证等

15. **WPS 工具类**
    - 文件: `src/utils/wps-util.js`
    - 功能: WPS 文档处理相关功能

## 🔧 技术栈

- **框架**: Vue 2.x
- **路由**: Vue Router
- **状态管理**: Vuex + vuex-persistedstate
- **HTTP 客户端**: Axios
- **UI 组件库**: Element UI
- **微前端**: qiankun
- **构建工具**: Webpack

## 🚀 核心特性

### 微前端支持
- 完整的 qiankun 生命周期管理
- 全局状态通信机制
- 主应用与子应用数据同步

### 状态管理
- 人员信息 (personInfo)
- 案件信息 (caseInfo)
- UI 状态 (ui)
- 审查状态 (review)
- 系统配置 (system)

### HTTP 通信
- 统一的请求/响应拦截器
- 自动 Token 管理
- 完善的错误处理机制
- 请求重试和取消

### 数据处理
- 树形数据转换工具
- 日期格式化和计算
- WPS 文档处理集成

## 📋 下一步计划

### 阶段二: 核心服务层
- [ ] 配置数据层模块 (0b43, 2386, 548b, 708c, 8d71, 9159, af49, bf6f)
- [ ] API 服务层模块
- [ ] 数据验证和处理模块

### 阶段三: 业务组件层
- [ ] 核心业务组件 (1e4b, 0ab5 等)
- [ ] 表单组件和验证
- [ ] 数据展示组件

### 阶段四: 功能增强层
- [ ] 思维导图功能模块 (6f50, feda 等)
- [ ] 文件处理模块
- [ ] 高级交互组件

## 🔍 使用说明

### 开发环境启动
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run serve

# 构建生产版本
npm run build
```

### 微前端集成
```javascript
// 在主应用中注册子应用
import { registerMicroApps } from 'qiankun'

registerMicroApps([
  {
    name: 'zjsc',
    entry: '//localhost:8080/zjsc',
    container: '#subapp-container',
    activeRule: '/zjsc',
  },
])
```

### 状态管理使用
```javascript
// 在组件中使用 Vuex
this.$store.dispatch('updatePersonInfo', personData)
this.$store.getters.personInfo

// 使用工具类
this.$TreeUtil.getTreeData(flatData)
this.$DateUtil.format(new Date(), 'YYYY-MM-DD')
```

## ⚠️ 注意事项

1. **微前端环境**: 确保在 qiankun 环境中正确设置全局状态管理
2. **Token 管理**: HTTP 客户端会自动处理 Token 的添加和更新
3. **状态持久化**: 关键状态会自动保存到 sessionStorage
4. **错误处理**: 统一的错误处理机制，避免重复的错误提示

## 📞 技术支持

如有问题，请参考：
- 模块还原文档: `/zjsc/docs/modules/`
- 原始分析文档: `/zjsc/webpack-modules-map.md`
- 还原计划: `/zjsc/module-rewrite-todo.md`

---

**项目状态**: 基础架构层已完成 ✅  
**当前版本**: 1.0.0  
**最后更新**: 2025-01-04
