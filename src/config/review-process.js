/**
 * ZJSC 证据审查系统 - 审查流程配置
 * 
 * Module ID: 708c
 * 功能: 审查流程步骤配置，包含案件基本信息、侦查意见、证据审查、审查认定、审查意见等流程步骤
 * 
 * 数据结构说明：
 * - bzbm: 步骤编码
 * - bzmc: 步骤名称
 */

/**
 * 审查流程步骤配置
 * 定义了证据审查的完整流程步骤
 */
export const reviewProcessSteps = [
  {
    bzbm: "ajjbxx",
    bzmc: "案件基本信息"
  },
  {
    bzbm: "zcyj",
    bzmc: "侦查意见"
  },
  {
    bzbm: "zjsc",
    bzmc: "证据审查"
  },
  {
    bzbm: "scrd",
    bzmc: "审查认定"
  },
  {
    bzbm: "scyj",
    bzmc: "审查意见"
  }
]

/**
 * 审查流程常量定义
 */
export const REVIEW_PROCESS_CONSTANTS = {
  // 步骤编码
  STEP_CODES: {
    CASE_INFO: 'ajjbxx',           // 案件基本信息
    INVESTIGATION_OPINION: 'zcyj', // 侦查意见
    EVIDENCE_REVIEW: 'zjsc',       // 证据审查
    REVIEW_DETERMINATION: 'scrd',  // 审查认定
    REVIEW_OPINION: 'scyj'         // 审查意见
  },

  // 步骤名称
  STEP_NAMES: {
    ajjbxx: '案件基本信息',
    zcyj: '侦查意见',
    zjsc: '证据审查',
    scrd: '审查认定',
    scyj: '审查意见'
  },

  // 步骤顺序
  STEP_ORDER: ['ajjbxx', 'zcyj', 'zjsc', 'scrd', 'scyj']
}

/**
 * 详细的流程步骤配置
 */
export const detailedProcessConfig = {
  // 案件基本信息
  ajjbxx: {
    code: 'ajjbxx',
    name: '案件基本信息',
    description: '记录案件的基本信息，包括案件程序情况、犯罪嫌疑人信息、被害人信息、发破案经过等',
    order: 1,
    required: true,
    sections: [
      {
        id: 'case_procedure',
        name: '案件程序情况',
        description: '案件的受理、立案等程序信息'
      },
      {
        id: 'suspect_info',
        name: '犯罪嫌疑人基本情况',
        description: '犯罪嫌疑人的身份信息、前科劣迹、强制措施等'
      },
      {
        id: 'victim_info',
        name: '被害人基本情况',
        description: '被害人的身份信息和相关情况'
      },
      {
        id: 'case_discovery',
        name: '发、破案经过',
        description: '案件的发现和侦破过程'
      }
    ]
  },

  // 侦查意见
  zcyj: {
    code: 'zcyj',
    name: '侦查意见',
    description: '侦查机关对案件事实的认定和处理意见',
    order: 2,
    required: true,
    sections: [
      {
        id: 'agency_determination',
        name: '移送机关认定',
        description: '侦查机关认定的犯罪事实'
      },
      {
        id: 'agency_opinion',
        name: '移送机关处理意见',
        description: '侦查机关的法律适用和处理建议'
      }
    ]
  },

  // 证据审查
  zjsc: {
    code: 'zjsc',
    name: '证据审查',
    description: '对案件证据进行全面审查和分析',
    order: 3,
    required: true,
    sections: [
      {
        id: 'evidence_analysis',
        name: '证据分析',
        description: '按照证据类型进行分类审查'
      },
      {
        id: 'evidence_chain',
        name: '证据链分析',
        description: '分析证据之间的关联性和完整性'
      },
      {
        id: 'evidence_problems',
        name: '证据问题',
        description: '发现的证据问题和不足'
      }
    ]
  },

  // 审查认定
  scrd: {
    code: 'scrd',
    name: '审查认定',
    description: '检察机关对案件事实和证据的认定',
    order: 4,
    required: true,
    sections: [
      {
        id: 'fact_determination',
        name: '审查认定的事实',
        description: '检察机关认定的案件事实'
      },
      {
        id: 'special_issues',
        name: '需要说明的问题及有关情况',
        description: '案件背景、管辖、同案犯处理等特殊情况'
      },
      {
        id: 'participant_opinions',
        name: '相关诉讼参与人的意见',
        description: '被害人、辩护人等的意见'
      }
    ]
  },

  // 审查意见
  scyj: {
    code: 'scyj',
    name: '审查意见',
    description: '检察机关的最终审查意见和处理决定',
    order: 5,
    required: true,
    sections: [
      {
        id: 'fact_evidence_opinion',
        name: '对全案事实证据情况的认定意见',
        description: '对案件事实和证据的综合评价'
      },
      {
        id: 'legal_application',
        name: '对案件法律适用的认定意见',
        description: '法律条文的适用和罪名认定'
      },
      {
        id: 'plea_bargaining',
        name: '认罪认罚情况',
        description: '犯罪嫌疑人的认罪认罚情况'
      },
      {
        id: 'sentencing_analysis',
        name: '量刑分析',
        description: '量刑建议和分析'
      },
      {
        id: 'detention_review',
        name: '羁押必要性审查意见',
        description: '对羁押必要性的审查'
      },
      {
        id: 'supervision_opinion',
        name: '侦查监督及追诉漏罪漏犯意见',
        description: '侦查监督和追诉意见'
      },
      {
        id: 'prosecutor_opinion',
        name: '承办人认为',
        description: '承办检察官的最终意见'
      }
    ]
  }
}

/**
 * 根据步骤编码获取步骤名称
 * @param {string} stepCode - 步骤编码
 * @returns {string} 步骤名称
 */
export function getStepName(stepCode) {
  return REVIEW_PROCESS_CONSTANTS.STEP_NAMES[stepCode] || ''
}

/**
 * 根据步骤编码获取详细配置
 * @param {string} stepCode - 步骤编码
 * @returns {Object|null} 详细配置
 */
export function getStepConfig(stepCode) {
  return detailedProcessConfig[stepCode] || null
}

/**
 * 获取所有流程步骤
 * @returns {Array} 流程步骤数组
 */
export function getAllProcessSteps() {
  return reviewProcessSteps
}

/**
 * 获取下一个步骤
 * @param {string} currentStep - 当前步骤编码
 * @returns {string|null} 下一个步骤编码
 */
export function getNextStep(currentStep) {
  const currentIndex = REVIEW_PROCESS_CONSTANTS.STEP_ORDER.indexOf(currentStep)
  if (currentIndex >= 0 && currentIndex < REVIEW_PROCESS_CONSTANTS.STEP_ORDER.length - 1) {
    return REVIEW_PROCESS_CONSTANTS.STEP_ORDER[currentIndex + 1]
  }
  return null
}

/**
 * 获取上一个步骤
 * @param {string} currentStep - 当前步骤编码
 * @returns {string|null} 上一个步骤编码
 */
export function getPreviousStep(currentStep) {
  const currentIndex = REVIEW_PROCESS_CONSTANTS.STEP_ORDER.indexOf(currentStep)
  if (currentIndex > 0) {
    return REVIEW_PROCESS_CONSTANTS.STEP_ORDER[currentIndex - 1]
  }
  return null
}

/**
 * 检查步骤是否有效
 * @param {string} stepCode - 步骤编码
 * @returns {boolean} 是否有效
 */
export function isValidStep(stepCode) {
  return REVIEW_PROCESS_CONSTANTS.STEP_ORDER.includes(stepCode)
}

/**
 * 获取步骤进度百分比
 * @param {string} currentStep - 当前步骤编码
 * @returns {number} 进度百分比 (0-100)
 */
export function getStepProgress(currentStep) {
  const currentIndex = REVIEW_PROCESS_CONSTANTS.STEP_ORDER.indexOf(currentStep)
  if (currentIndex >= 0) {
    return Math.round(((currentIndex + 1) / REVIEW_PROCESS_CONSTANTS.STEP_ORDER.length) * 100)
  }
  return 0
}

/**
 * 获取流程完成状态
 * @param {Array} completedSteps - 已完成的步骤数组
 * @returns {Object} 完成状态信息
 */
export function getProcessCompletionStatus(completedSteps = []) {
  const totalSteps = REVIEW_PROCESS_CONSTANTS.STEP_ORDER.length
  const completedCount = completedSteps.filter(step => 
    REVIEW_PROCESS_CONSTANTS.STEP_ORDER.includes(step)
  ).length
  
  return {
    totalSteps,
    completedCount,
    completionRate: Math.round((completedCount / totalSteps) * 100),
    isComplete: completedCount === totalSteps,
    remainingSteps: REVIEW_PROCESS_CONSTANTS.STEP_ORDER.filter(step => 
      !completedSteps.includes(step)
    )
  }
}

// 默认导出
export default {
  reviewProcessSteps,
  REVIEW_PROCESS_CONSTANTS,
  detailedProcessConfig,
  getStepName,
  getStepConfig,
  getAllProcessSteps,
  getNextStep,
  getPreviousStep,
  isValidStep,
  getStepProgress,
  getProcessCompletionStatus
}
