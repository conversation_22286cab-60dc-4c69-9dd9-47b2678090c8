/**
 * ZJSC 证据审查系统 - 审查项目配置
 * 
 * Module ID: bf6f
 * 功能: 审查项目详细配置，包含具体的审查项目、审查明细、引用文件等
 * 
 * 数据结构说明：
 * - scxbh: 审查项编号
 * - scxmc: 审查项名称
 * - sfqr: 是否确认
 * - sfzsyy: 是否展示摘要
 * - scmxList: 审查明细列表
 * - yywjList: 引用文件列表
 * - wtList: 问题列表
 * - ysList: 意见列表
 * - bdpz: 绑定凭证
 * - anpz: 案件凭证
 */

/**
 * 审查项目配置数据
 * 包含具体的审查项目和相关配置信息
 */
export const reviewItemsConfig = [
  {
    scxbh: "312AE85607A54C7886CDB8AC17CF554A",
    scxmc: "书证-情况说明分析",
    sfqr: "N",
    sfzsyy: "Y",
    scmxList: [
      {
        scmxbh: "AB12557F0C0B443E9CDC5DA726B4F97B",
        scmxmc: "摘要",
        scmxz: "卫*承认于2024年6月1日中午在杭州崇贤鸦雀洋停车场内驾驶鲁Q596UX重型半挂牵引车时，饮用了两瓶620ML的大乌苏啤酒，血液酒精浓度检测结果为170mg/100ml。",
        sfzsmc: "Y",
        sywjList: []
      },
      {
        scmxbh: "E926DB44A704455EAB5464FF8DB19CF1",
        scmxmc: "证明",
        scmxz: "卫*的行为符合醉酒驾驶的法律规定，血液酒精浓度超过80mg/100ml，构成危险驾驶罪。",
        sfzsmc: "Y",
        sywjList: []
      },
      {
        scmxbh: "E926DB44A70435EAB5464FF8DB19CF1",
        scmxmc: "问题说明",
        scmxz: "无",
        sfzsmc: "Y",
        sywjList: []
      }
    ],
    yywjList: [
      {
        wjbh: "7a9142a95309456dbcc3bd375e7ed11d",
        wjmc: "卫刚_询问笔录_第undefined次",
        jzfl: "dzjz",
        mlfl: "yjml"
      }
    ],
    wtList: [],
    ysList: [],
    bdpz: null,
    anpz: ["jx"]
  }
]

/**
 * 审查项目常量定义
 */
export const REVIEW_ITEM_CONSTANTS = {
  // 确认状态
  CONFIRM_STATUS: {
    YES: 'Y',
    NO: 'N'
  },

  // 文件分类
  FILE_CATEGORIES: {
    DZJZ: 'dzjz',  // 电子卷宗
    YJML: 'yjml'   // 引用目录
  },

  // 审查明细类型
  DETAIL_TYPES: {
    SUMMARY: '摘要',
    PROOF: '证明',
    PROBLEM: '问题说明',
    ANALYSIS: '分析',
    CONCLUSION: '结论'
  },

  // 案件凭证类型
  CASE_EVIDENCE_TYPES: {
    JX: 'jx'  // 检验
  }
}

/**
 * 审查项目模板配置
 */
export const reviewItemTemplates = {
  // 书证审查模板
  documentaryEvidence: {
    scxmc: "书证审查",
    sfqr: "N",
    sfzsyy: "Y",
    scmxList: [
      {
        scmxmc: "摘要",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      },
      {
        scmxmc: "真实性分析",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      },
      {
        scmxmc: "关联性分析",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      },
      {
        scmxmc: "合法性分析",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      },
      {
        scmxmc: "问题说明",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      }
    ],
    yywjList: [],
    wtList: [],
    ysList: [],
    bdpz: null,
    anpz: []
  },

  // 证人证言审查模板
  witnessTestimony: {
    scxmc: "证人证言审查",
    sfqr: "N",
    sfzsyy: "Y",
    scmxList: [
      {
        scmxmc: "证人基本情况",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      },
      {
        scmxmc: "证言内容摘要",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      },
      {
        scmxmc: "证言可信度分析",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      },
      {
        scmxmc: "与其他证据的印证关系",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      },
      {
        scmxmc: "问题说明",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      }
    ],
    yywjList: [],
    wtList: [],
    ysList: [],
    bdpz: null,
    anpz: []
  },

  // 鉴定意见审查模板
  expertOpinion: {
    scxmc: "鉴定意见审查",
    sfqr: "N",
    sfzsyy: "Y",
    scmxList: [
      {
        scmxmc: "鉴定机构资质",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      },
      {
        scmxmc: "鉴定人员资格",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      },
      {
        scmxmc: "鉴定程序合法性",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      },
      {
        scmxmc: "鉴定方法科学性",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      },
      {
        scmxmc: "鉴定结论可靠性",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      },
      {
        scmxmc: "问题说明",
        scmxz: "",
        sfzsmc: "Y",
        sywjList: []
      }
    ],
    yywjList: [],
    wtList: [],
    ysList: [],
    bdpz: null,
    anpz: []
  }
}

/**
 * 根据审查项编号获取审查项配置
 * @param {string} scxbh - 审查项编号
 * @returns {Object|null} 审查项配置
 */
export function getReviewItemById(scxbh) {
  return reviewItemsConfig.find(item => item.scxbh === scxbh) || null
}

/**
 * 根据审查项名称获取审查项配置
 * @param {string} scxmc - 审查项名称
 * @returns {Object|null} 审查项配置
 */
export function getReviewItemByName(scxmc) {
  return reviewItemsConfig.find(item => item.scxmc === scxmc) || null
}

/**
 * 获取所有审查项配置
 * @returns {Array} 审查项配置数组
 */
export function getAllReviewItems() {
  return reviewItemsConfig
}

/**
 * 根据模板类型创建新的审查项
 * @param {string} templateType - 模板类型
 * @param {Object} customData - 自定义数据
 * @returns {Object} 新的审查项配置
 */
export function createReviewItemFromTemplate(templateType, customData = {}) {
  const template = reviewItemTemplates[templateType]
  if (!template) {
    throw new Error(`未找到模板类型: ${templateType}`)
  }

  // 生成新的审查项编号
  const scxbh = generateReviewItemId()
  
  // 为审查明细生成编号
  const scmxList = template.scmxList.map(detail => ({
    ...detail,
    scmxbh: generateReviewDetailId()
  }))

  return {
    ...template,
    scxbh,
    scmxList,
    ...customData
  }
}

/**
 * 生成审查项编号
 * @returns {string} 审查项编号
 */
export function generateReviewItemId() {
  return 'SCXBH_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9).toUpperCase()
}

/**
 * 生成审查明细编号
 * @returns {string} 审查明细编号
 */
export function generateReviewDetailId() {
  return 'SCMXBH_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9).toUpperCase()
}

/**
 * 验证审查项配置的完整性
 * @param {Object} reviewItem - 审查项配置
 * @returns {Object} 验证结果
 */
export function validateReviewItem(reviewItem) {
  const errors = []
  
  if (!reviewItem.scxbh) {
    errors.push('缺少审查项编号')
  }
  
  if (!reviewItem.scxmc) {
    errors.push('缺少审查项名称')
  }
  
  if (!Array.isArray(reviewItem.scmxList)) {
    errors.push('审查明细列表格式错误')
  } else {
    reviewItem.scmxList.forEach((detail, index) => {
      if (!detail.scmxbh) {
        errors.push(`第${index + 1}个审查明细缺少编号`)
      }
      if (!detail.scmxmc) {
        errors.push(`第${index + 1}个审查明细缺少名称`)
      }
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 获取可用的模板类型
 * @returns {Array} 模板类型数组
 */
export function getAvailableTemplateTypes() {
  return Object.keys(reviewItemTemplates)
}

// 默认导出
export default {
  reviewItemsConfig,
  REVIEW_ITEM_CONSTANTS,
  reviewItemTemplates,
  getReviewItemById,
  getReviewItemByName,
  getAllReviewItems,
  createReviewItemFromTemplate,
  generateReviewItemId,
  generateReviewDetailId,
  validateReviewItem,
  getAvailableTemplateTypes
}
