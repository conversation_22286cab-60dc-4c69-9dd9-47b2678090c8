/**
 * ZJSC 证据审查系统 - 审查模式配置
 * 
 * Module ID: 548b
 * 功能: 审查模式配置，包含审查要点、证据分类、犯罪事实、犯罪主体等审查维度
 * 
 * 数据结构说明：
 * - msbm: 模式编码
 * - msmc: 模式名称
 */

/**
 * 审查模式数据
 * 定义了证据审查的四个主要维度
 */
export const reviewModes = [
  {
    msbm: "scyd",
    msmc: "审查要点"
  },
  {
    msbm: "zjfl",
    msmc: "证据分类"
  },
  {
    msbm: "fzss",
    msmc: "犯罪事实"
  },
  {
    msbm: "fzzt",
    msmc: "犯罪主体"
  }
]

/**
 * 审查模式常量定义
 */
export const REVIEW_MODE_CONSTANTS = {
  // 模式编码
  MODES: {
    REVIEW_POINTS: 'scyd',      // 审查要点
    EVIDENCE_CLASSIFICATION: 'zjfl',  // 证据分类
    CRIMINAL_FACTS: 'fzss',     // 犯罪事实
    CRIMINAL_SUBJECT: 'fzzt'    // 犯罪主体
  },

  // 模式名称
  MODE_NAMES: {
    scyd: '审查要点',
    zjfl: '证据分类',
    fzss: '犯罪事实',
    fzzt: '犯罪主体'
  }
}

/**
 * 审查要点详细配置
 */
export const reviewPointsConfig = {
  mode: 'scyd',
  name: '审查要点',
  description: '按照审查要点进行证据分析和评估',
  categories: [
    {
      id: 'evidence_authenticity',
      name: '证据真实性',
      description: '审查证据的真实性和可靠性',
      checkPoints: [
        '证据来源是否合法',
        '证据内容是否真实',
        '证据是否存在伪造、变造情况',
        '证据收集程序是否规范'
      ]
    },
    {
      id: 'evidence_relevance',
      name: '证据关联性',
      description: '审查证据与案件事实的关联程度',
      checkPoints: [
        '证据是否与案件事实相关',
        '证据能否证明待证事实',
        '证据之间是否存在矛盾',
        '证据链是否完整'
      ]
    },
    {
      id: 'evidence_legality',
      name: '证据合法性',
      description: '审查证据收集和保存的合法性',
      checkPoints: [
        '证据收集主体是否合法',
        '证据收集程序是否合法',
        '证据保存是否规范',
        '证据移送是否合法'
      ]
    }
  ]
}

/**
 * 证据分类详细配置
 */
export const evidenceClassificationConfig = {
  mode: 'zjfl',
  name: '证据分类',
  description: '按照证据类型进行分类审查',
  categories: [
    {
      id: 'documentary_evidence',
      name: '书证',
      description: '以文字、符号、图画等记载的内容证明案件事实的证据',
      types: ['合同文件', '财务凭证', '身份证件', '许可证书']
    },
    {
      id: 'physical_evidence',
      name: '物证',
      description: '以其外部特征、存在场所和物质属性证明案件事实的实物',
      types: ['作案工具', '赃款赃物', '痕迹物品', '其他实物']
    },
    {
      id: 'witness_testimony',
      name: '证人证言',
      description: '证人就其了解的案件情况向司法机关所作的陈述',
      types: ['目击证人', '知情证人', '专业证人', '品格证人']
    },
    {
      id: 'expert_opinion',
      name: '鉴定意见',
      description: '具有专门知识的人对专门性问题进行鉴定后出具的意见',
      types: ['法医鉴定', '物证鉴定', '文书鉴定', '其他鉴定']
    }
  ]
}

/**
 * 犯罪事实详细配置
 */
export const criminalFactsConfig = {
  mode: 'fzss',
  name: '犯罪事实',
  description: '按照犯罪构成要件审查案件事实',
  elements: [
    {
      id: 'criminal_object',
      name: '犯罪客体',
      description: '犯罪行为所侵害的社会关系',
      checkPoints: [
        '确定被侵害的法益',
        '分析侵害程度',
        '评估社会危害性'
      ]
    },
    {
      id: 'objective_aspect',
      name: '客观方面',
      description: '犯罪的外在表现',
      checkPoints: [
        '犯罪行为的具体表现',
        '犯罪结果的发生',
        '行为与结果的因果关系',
        '犯罪的时间、地点、方法'
      ]
    },
    {
      id: 'criminal_subject',
      name: '犯罪主体',
      description: '实施犯罪行为的人',
      checkPoints: [
        '行为人的刑事责任能力',
        '特殊主体身份',
        '共同犯罪情况'
      ]
    },
    {
      id: 'subjective_aspect',
      name: '主观方面',
      description: '犯罪人的心理态度',
      checkPoints: [
        '故意或过失的认定',
        '犯罪目的和动机',
        '认识因素和意志因素'
      ]
    }
  ]
}

/**
 * 犯罪主体详细配置
 */
export const criminalSubjectConfig = {
  mode: 'fzzt',
  name: '犯罪主体',
  description: '审查犯罪主体的身份和责任能力',
  aspects: [
    {
      id: 'capacity',
      name: '刑事责任能力',
      description: '行为人承担刑事责任的能力',
      checkPoints: [
        '年龄条件',
        '精神状态',
        '辨认能力',
        '控制能力'
      ]
    },
    {
      id: 'identity',
      name: '主体身份',
      description: '行为人的特殊身份',
      checkPoints: [
        '自然人身份',
        '特殊职务身份',
        '法定代表人身份',
        '其他特殊身份'
      ]
    },
    {
      id: 'joint_crime',
      name: '共同犯罪',
      description: '多人共同实施犯罪的情况',
      checkPoints: [
        '共同犯罪的认定',
        '主犯、从犯的区分',
        '教唆犯、胁从犯的认定',
        '犯罪集团的认定'
      ]
    }
  ]
}

/**
 * 根据模式编码获取模式名称
 * @param {string} modeCode - 模式编码
 * @returns {string} 模式名称
 */
export function getModeName(modeCode) {
  return REVIEW_MODE_CONSTANTS.MODE_NAMES[modeCode] || ''
}

/**
 * 根据模式编码获取详细配置
 * @param {string} modeCode - 模式编码
 * @returns {Object|null} 详细配置对象
 */
export function getModeConfig(modeCode) {
  switch (modeCode) {
    case REVIEW_MODE_CONSTANTS.MODES.REVIEW_POINTS:
      return reviewPointsConfig
    case REVIEW_MODE_CONSTANTS.MODES.EVIDENCE_CLASSIFICATION:
      return evidenceClassificationConfig
    case REVIEW_MODE_CONSTANTS.MODES.CRIMINAL_FACTS:
      return criminalFactsConfig
    case REVIEW_MODE_CONSTANTS.MODES.CRIMINAL_SUBJECT:
      return criminalSubjectConfig
    default:
      return null
  }
}

/**
 * 获取所有审查模式
 * @returns {Array} 审查模式数组
 */
export function getAllReviewModes() {
  return reviewModes
}

/**
 * 检查模式编码是否有效
 * @param {string} modeCode - 模式编码
 * @returns {boolean} 是否有效
 */
export function isValidModeCode(modeCode) {
  return Object.values(REVIEW_MODE_CONSTANTS.MODES).includes(modeCode)
}

/**
 * 获取默认审查模式
 * @returns {Object} 默认审查模式
 */
export function getDefaultReviewMode() {
  return reviewModes[0] // 默认返回第一个模式（审查要点）
}

// 默认导出
export default {
  reviewModes,
  REVIEW_MODE_CONSTANTS,
  reviewPointsConfig,
  evidenceClassificationConfig,
  criminalFactsConfig,
  criminalSubjectConfig,
  getModeName,
  getModeConfig,
  getAllReviewModes,
  isValidModeCode,
  getDefaultReviewMode
}
