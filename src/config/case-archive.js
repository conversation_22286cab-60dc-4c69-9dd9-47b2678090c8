/**
 * ZJSC 证据审查系统 - 案件档案数据配置
 * 
 * Module ID: 2386
 * 功能: 案件档案结构配置，包含证据材料卷、诉讼文书卷等档案分类
 * 
 * 数据结构说明：
 * - id: 唯一标识符
 * - pid: 父级ID
 * - label: 显示名称
 * - jdlx: 节点类型（目录/文件）
 * - wjlx: 文件类型（dossier表示档案文件）
 * - jdbs: 节点标识
 */

/**
 * 案件档案结构数据
 * 包含完整的案件档案分类和具体文件结构
 */
export const caseArchiveStructure = [
  // 根级档案分类
  {
    id: "cce334fe4ca9a2fd00ba3ac7fcf651b8",
    pid: "",
    label: "案件档案",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },

  // 一级分类：证据材料卷和诉讼文书卷
  {
    id: "6edf4a0d84e446529df861b9d888506c",
    pid: "cce334fe4ca9a2fd00ba3ac7fcf651b8",
    label: "证据材料卷",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "39edfa16ee384f398838470df69ee1a1",
    pid: "cce334fe4ca9a2fd00ba3ac7fcf651b8",
    label: "诉讼文书卷",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },

  // 证据材料卷子分类
  {
    id: "3c861e036e24a31119c5a9951ec97dc2",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "目录",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "cf77cffb434d4d44b269b566ff28679e",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "犯罪嫌疑人照片",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "862f05f2e90a4440acca6c982f117319",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "常住人口登记表",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "5bbf7ea8845c40bd982d151b8ad1a60a",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "查获经过",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "c5cf025bb9da4bb5b363d8f820a465f8",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "传讯通知书",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "b4f01a60d2d647f794eef345c79ad0db",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "犯罪嫌疑人诉讼权利义务告知书、自伤自残告知书",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "60f37d3c2e424504bd63b76c6929cffe",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "讯问笔录（孟庆远）",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "36b0457765a54e4395ce660fbf4b6112",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "询问笔录（徐好）",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "53093fdbd0e944b493e2bea4a74a984f",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "呼吸式酒精测试单、交通管理行政强制措施凭证",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "2e4c873e37b7401696196e5aaf85905c",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "呼气检测照片、抽血照片、血样提取登记表",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "5ee42b1e7f8e428fb2b8c5c3a7e3e01c",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "鉴定聘请书、鉴定意见通知书",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "6434e8df0be24af78884cf9a4cf10ed7",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "司法鉴定意见书",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "9ae9f1d20eea4d2aac594b797e169c2a",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "驾驶员信息、车辆信息、行车路线图",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "1be5d413002d44bba354e5825f794672",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "其他材料、户籍资料",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "4d3f8b98d69e4b00b7d7fc1359804a72",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "随身物品代保管清单",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "2c89765f4ebc4ce3996f269dfa848c15",
    pid: "6edf4a0d84e446529df861b9d888506c",
    label: "违法犯罪嫌疑人员初查工作项目表",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },

  // 诉讼文书卷子分类
  {
    id: "656b7ac646a51b512e5efbdfc038d4de",
    pid: "39edfa16ee384f398838470df69ee1a1",
    label: "目录",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "637fc13b04be4847880161730540320a",
    pid: "39edfa16ee384f398838470df69ee1a1",
    label: "案件接报回执单、受案登记表、受案回执",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "4a44ee923d6b4fd8bcec7def1f282213",
    pid: "39edfa16ee384f398838470df69ee1a1",
    label: "立案决定书、立案告知书",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "f02c28d74b004c53b40f27b8739540e0",
    pid: "39edfa16ee384f398838470df69ee1a1",
    label: "拘留证、拘留通知书、变更羁押期限通知书",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "5b239e341a7c4f3a9b1c3de0f7fa59fb",
    pid: "39edfa16ee384f398838470df69ee1a1",
    label: "释放通知书、释放证明书",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "17240882afba4a938b4e7ff6698882ac",
    pid: "39edfa16ee384f398838470df69ee1a1",
    label: "取保候审决定书、取保候审执行通知书",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "77504bf7b91f4d378542f8628a3e00f4",
    pid: "39edfa16ee384f398838470df69ee1a1",
    label: "被取保候审人义务告知书",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "61b09af1911b4ec28b06a9c980f8cecf",
    pid: "39edfa16ee384f398838470df69ee1a1",
    label: "取保候审保证书",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "cb028d1ea5a447f6a57902d122fcf5b0",
    pid: "39edfa16ee384f398838470df69ee1a1",
    label: "起诉意见书",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  }
]

/**
 * 案件档案常量定义
 */
export const CASE_ARCHIVE_CONSTANTS = {
  // 档案类型
  ARCHIVE_TYPES: {
    EVIDENCE_VOLUME: '6edf4a0d84e446529df861b9d888506c',    // 证据材料卷
    LITIGATION_VOLUME: '39edfa16ee384f398838470df69ee1a1'    // 诉讼文书卷
  },

  // 文件类型
  FILE_TYPES: {
    DOSSIER: 'dossier'  // 档案文件
  },

  // 节点类型
  NODE_TYPES: {
    DIRECTORY: '目录',
    FILE: '文件'
  }
}

/**
 * 获取案件档案树形结构
 * @returns {Array} 树形结构的案件档案数据
 */
export function getCaseArchiveTree() {
  // 使用 TreeUtil 将扁平数组转换为树形结构
  if (typeof window !== 'undefined' && window.Vue && window.Vue.prototype.$TreeUtil) {
    return window.Vue.prototype.$TreeUtil.getTreeData(caseArchiveStructure, 'id', 'pid')
  }
  
  // 备用的简单树形转换
  const rootNodes = caseArchiveStructure.filter(item => !item.pid)
  const buildTree = (nodes) => {
    return nodes.map(node => {
      const children = caseArchiveStructure.filter(item => item.pid === node.id)
      return {
        ...node,
        children: children.length > 0 ? buildTree(children) : []
      }
    })
  }
  
  return buildTree(rootNodes)
}

/**
 * 根据ID获取档案节点信息
 * @param {string} id - 档案节点ID
 * @returns {Object|null} 档案节点信息
 */
export function getArchiveNodeById(id) {
  return caseArchiveStructure.find(item => item.id === id) || null
}

/**
 * 根据父级ID获取子档案节点
 * @param {string} pid - 父级ID
 * @returns {Array} 子档案节点数组
 */
export function getArchiveNodesByParentId(pid) {
  return caseArchiveStructure.filter(item => item.pid === pid)
}

/**
 * 获取证据材料卷节点
 * @returns {Array} 证据材料卷节点数组
 */
export function getEvidenceVolumeNodes() {
  return caseArchiveStructure.filter(item => 
    item.pid === CASE_ARCHIVE_CONSTANTS.ARCHIVE_TYPES.EVIDENCE_VOLUME
  )
}

/**
 * 获取诉讼文书卷节点
 * @returns {Array} 诉讼文书卷节点数组
 */
export function getLitigationVolumeNodes() {
  return caseArchiveStructure.filter(item => 
    item.pid === CASE_ARCHIVE_CONSTANTS.ARCHIVE_TYPES.LITIGATION_VOLUME
  )
}

/**
 * 检查是否为档案文件
 * @param {Object} archiveNode - 档案节点对象
 * @returns {boolean} 是否为档案文件
 */
export function isDossierFile(archiveNode) {
  return archiveNode && archiveNode.wjlx === CASE_ARCHIVE_CONSTANTS.FILE_TYPES.DOSSIER
}

// 默认导出
export default {
  caseArchiveStructure,
  CASE_ARCHIVE_CONSTANTS,
  getCaseArchiveTree,
  getArchiveNodeById,
  getArchiveNodesByParentId,
  getEvidenceVolumeNodes,
  getLitigationVolumeNodes,
  isDossierFile
}
