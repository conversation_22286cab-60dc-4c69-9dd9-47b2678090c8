/**
 * ZJSC 证据审查系统 - 证据分类数据配置
 * 
 * Module ID: 0b43
 * 功能: 证据类型分类配置，包含书证、证人证言、被害人陈述等证据类型
 * 
 * 数据结构说明：
 * - id: 唯一标识符
 * - pid: 父级ID，空字符串表示根级
 * - label: 显示名称
 * - jdlx: 节点类型（目录/文件）
 * - wjlx: 文件类型
 * - jdbs: 节点标识
 */

/**
 * 证据分类数据
 * 包含司法审查中常见的证据类型和具体文件分类
 */
export const evidenceTypes = [
  // 根级证据类型
  {
    id: "3c861e036e24a31119c5a9951ec97dc2",
    pid: "",
    label: "书证",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "39edfa16ee384f398838470df69ee1a1",
    pid: "",
    label: "证人证言",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "656b7ac646a51b512e5efbdfc038d4de",
    pid: "",
    label: "被害人陈述",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "637fc13b04be4847880161730540320a",
    pid: "",
    label: "案件接报回执单、受案登记表、受案回执",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "6edf4a0d84e446529df861b9d888506c",
    pid: "",
    label: "鉴定意见",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "cf77cffb434d4d44b269b566ff28679e",
    pid: "",
    label: "勘验、检查、辨认、侦查实验等笔录",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "862f05f2e90a4440acca6c982f117319",
    pid: "",
    label: "视听资料、电子数据",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },
  {
    id: "4a44ee923d6b4fd8bcec7def1f282213",
    pid: "",
    label: "物证",
    jdlx: "目录",
    wjlx: null,
    jdbs: null
  },

  // 书证子类
  {
    id: "f02c28d74b004c53b40f27b8739540e0",
    pid: "3c861e036e24a31119c5a9951ec97dc2",
    label: "受案登记表",
    jdlx: "文件",
    wjlx: null,
    jdbs: null
  },
  {
    id: "5bbf7ea8845c40bd982d151b8ad1a60a",
    pid: "3c861e036e24a31119c5a9951ec97dc2",
    label: "立案决定书",
    jdlx: "文件",
    wjlx: null,
    jdbs: null
  },

  // 证人证言子类
  {
    id: "5b239e341a7c4f3a9b1c3de0f7fa59fb",
    pid: "39edfa16ee384f398838470df69ee1a1",
    label: "询问笔录",
    jdlx: "文件",
    wjlx: null,
    jdbs: null
  },
  {
    id: "17240882afba4a938b4e7ff6698882ac",
    pid: "39edfa16ee384f398838470df69ee1a1",
    label: "询问通知书",
    jdlx: "文件",
    wjlx: null,
    jdbs: null
  },

  // 被害人陈述子类
  {
    id: "b4f01a60d2d647f794eef345c79ad0db",
    pid: "656b7ac646a51b512e5efbdfc038d4de",
    label: "询问笔录",
    jdlx: "文件",
    wjlx: null,
    jdbs: null
  }
]

/**
 * 证据类型常量定义
 */
export const EVIDENCE_TYPE_CONSTANTS = {
  // 节点类型
  NODE_TYPES: {
    DIRECTORY: '目录',
    FILE: '文件'
  },

  // 根级证据类型ID
  ROOT_TYPES: {
    DOCUMENTARY_EVIDENCE: '3c861e036e24a31119c5a9951ec97dc2',      // 书证
    WITNESS_TESTIMONY: '39edfa16ee384f398838470df69ee1a1',         // 证人证言
    VICTIM_STATEMENT: '656b7ac646a51b512e5efbdfc038d4de',          // 被害人陈述
    CASE_RECEIPT: '637fc13b04be4847880161730540320a',              // 案件接报回执单
    EXPERT_OPINION: '6edf4a0d84e446529df861b9d888506c',           // 鉴定意见
    INVESTIGATION_RECORD: 'cf77cffb434d4d44b269b566ff28679e',     // 勘验检查笔录
    AUDIOVISUAL_DATA: '862f05f2e90a4440acca6c982f117319',         // 视听资料
    PHYSICAL_EVIDENCE: '4a44ee923d6b4fd8bcec7def1f282213'         // 物证
  }
}

/**
 * 获取证据类型树形结构
 * @returns {Array} 树形结构的证据类型数据
 */
export function getEvidenceTypeTree() {
  // 使用 TreeUtil 将扁平数组转换为树形结构
  if (typeof window !== 'undefined' && window.Vue && window.Vue.prototype.$TreeUtil) {
    return window.Vue.prototype.$TreeUtil.getTreeData(evidenceTypes, 'id', 'pid')
  }
  
  // 备用的简单树形转换
  const rootNodes = evidenceTypes.filter(item => !item.pid)
  const buildTree = (nodes) => {
    return nodes.map(node => {
      const children = evidenceTypes.filter(item => item.pid === node.id)
      return {
        ...node,
        children: children.length > 0 ? buildTree(children) : []
      }
    })
  }
  
  return buildTree(rootNodes)
}

/**
 * 根据ID获取证据类型信息
 * @param {string} id - 证据类型ID
 * @returns {Object|null} 证据类型信息
 */
export function getEvidenceTypeById(id) {
  return evidenceTypes.find(item => item.id === id) || null
}

/**
 * 根据父级ID获取子证据类型
 * @param {string} pid - 父级ID
 * @returns {Array} 子证据类型数组
 */
export function getEvidenceTypesByParentId(pid) {
  return evidenceTypes.filter(item => item.pid === pid)
}

/**
 * 获取根级证据类型
 * @returns {Array} 根级证据类型数组
 */
export function getRootEvidenceTypes() {
  return evidenceTypes.filter(item => !item.pid)
}

/**
 * 检查是否为目录类型
 * @param {Object} evidenceType - 证据类型对象
 * @returns {boolean} 是否为目录类型
 */
export function isDirectory(evidenceType) {
  return evidenceType && evidenceType.jdlx === EVIDENCE_TYPE_CONSTANTS.NODE_TYPES.DIRECTORY
}

/**
 * 检查是否为文件类型
 * @param {Object} evidenceType - 证据类型对象
 * @returns {boolean} 是否为文件类型
 */
export function isFile(evidenceType) {
  return evidenceType && evidenceType.jdlx === EVIDENCE_TYPE_CONSTANTS.NODE_TYPES.FILE
}

// 默认导出
export default {
  evidenceTypes,
  EVIDENCE_TYPE_CONSTANTS,
  getEvidenceTypeTree,
  getEvidenceTypeById,
  getEvidenceTypesByParentId,
  getRootEvidenceTypes,
  isDirectory,
  isFile
}
