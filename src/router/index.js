/**
 * ZJSC 证据审查系统 - Vue Router 路由配置
 * 
 * Module ID: 88ed
 * 功能: Vue Router 路由配置，包含路由表和导航守卫
 * 
 * 管理应用的所有路由配置和权限控制
 */

import Vue from 'vue';
import VueRouter from 'vue-router';

// 安装Vue Router
Vue.use(VueRouter);

// 路由配置
const routes = [
  {
    path: '/',
    name: 'index',
    component: () => import('@/views/ReviewMain.vue'),
    meta: {
      title: '证据审查系统',
      requiresAuth: false,
      keepAlive: true
    }
  },
  {
    path: '/index',
    name: 'home',
    component: () => import('@/views/ReviewMain.vue'),
    meta: {
      title: '证据审查系统',
      requiresAuth: false,
      keepAlive: true
    }
  },
  {
    path: '/test',
    name: 'test',
    component: () => import('@/views/MindMapTest.vue'),
    meta: {
      title: '思维导图测试',
      requiresAuth: false,
      keepAlive: false
    }
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面不存在',
      requiresAuth: false,
      keepAlive: false
    }
  },
  {
    path: '*',
    redirect: '/404'
  }
];

// 路由配置选项
const routerOptions = {
  mode: 'history',
  base: process.env.VUE_APP_PUBLIC_PATH || '/',
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { x: 0, y: 0 };
    }
  }
};

// 创建路由实例
const router = new VueRouter(routerOptions);

// ========== 导航守卫 ==========

/**
 * 全局前置守卫
 */
router.beforeEach((to, from, next) => {
  console.log('路由导航:', { from: from.path, to: to.path });
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title;
  }
  
  // 检查认证状态
  const isAuthenticated = checkAuthentication();
  
  // 公开路径列表
  const publicPaths = ['/', '/index', '/404'];
  
  // 检查是否需要认证
  if (to.meta.requiresAuth && !isAuthenticated) {
    console.log('需要认证但用户未登录，重定向到首页');
    return next('/');
  }
  
  // 检查是否在公开路径中
  if (publicPaths.indexOf(to.path) < 0 && !isAuthenticated) {
    console.log('非公开路径且用户未登录，重定向到首页');
    return next('/');
  }
  
  // 检查微前端环境
  if (window.__POWERED_BY_QIANKUN__) {
    // 在微前端环境中，可以添加额外的权限检查
    const hasPermission = checkMicroFrontendPermission(to);
    if (!hasPermission) {
      console.log('微前端权限检查失败');
      return next('/404');
    }
  }
  
  // 设置loading状态
  setRouteLoading(true);
  
  next();
});

/**
 * 全局后置钩子
 */
router.afterEach((to, from) => {
  console.log('路由导航完成:', { from: from.path, to: to.path });
  
  // 清除loading状态
  setRouteLoading(false);
  
  // 记录页面访问
  logPageView(to);
  
  // 发送页面浏览事件
  emitPageViewEvent(to);
});

/**
 * 全局错误处理
 */
router.onError((error) => {
  console.error('路由错误:', error);
  
  // 处理路由加载错误
  if (error.name === 'ChunkLoadError') {
    console.error('路由组件加载失败:', error);
    // 可以在这里添加重试逻辑或错误提示
    handleChunkLoadError(error);
  }
});

// ========== 工具方法 ==========

/**
 * 检查用户认证状态
 * @returns {boolean} 是否已认证
 */
function checkAuthentication() {
  try {
    // 从sessionStorage获取Vuex状态
    const vuexData = sessionStorage.getItem('vuex');
    if (!vuexData) {
      return false;
    }
    
    const parsedData = JSON.parse(vuexData);
    
    // 检查用户信息是否存在
    if (parsedData.personInfo && parsedData.personInfo.id) {
      return true;
    }
    
    // 检查案件信息是否存在
    if (parsedData.caseInfo && parsedData.caseInfo.id) {
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('认证状态检查失败:', error);
    return false;
  }
}

/**
 * 检查微前端权限
 * @param {Object} to - 目标路由
 * @returns {boolean} 是否有权限
 */
function checkMicroFrontendPermission(to) {
  // 在微前端环境中检查权限
  if (window.qiankunActions && window.qiankunActions.getProps) {
    const props = window.qiankunActions.getProps();
    
    // 可以根据props中的权限信息进行检查
    if (props.permissions && props.permissions.length > 0) {
      // 这里可以实现具体的权限检查逻辑
      return true;
    }
  }
  
  return true; // 默认允许访问
}

/**
 * 设置路由loading状态
 * @param {boolean} isLoading - 是否加载中
 */
function setRouteLoading(isLoading) {
  // 可以在这里实现页面loading效果
  if (isLoading) {
    console.log('路由加载开始');
    // 显示loading
    document.body.classList.add('route-loading');
  } else {
    console.log('路由加载完成');
    // 隐藏loading
    document.body.classList.remove('route-loading');
  }
}

/**
 * 记录页面访问
 * @param {Object} route - 路由信息
 */
function logPageView(route) {
  // 记录页面访问日志
  const pageViewData = {
    path: route.path,
    name: route.name,
    title: route.meta.title,
    timestamp: Date.now(),
    userAgent: navigator.userAgent,
    referrer: document.referrer
  };
  
  console.log('页面访问记录:', pageViewData);
  
  // 可以发送到分析服务
  // sendToAnalytics(pageViewData);
}

/**
 * 发送页面浏览事件
 * @param {Object} route - 路由信息
 */
function emitPageViewEvent(route) {
  // 发发自定义事件
  const event = new CustomEvent('zjsc:pageView', {
    detail: {
      route,
      timestamp: Date.now()
    }
  });
  
  window.dispatchEvent(event);
}

/**
 * 处理路由组件加载错误
 * @param {Error} error - 错误对象
 */
function handleChunkLoadError(error) {
  console.error('处理路由组件加载错误:', error);
  
  // 可以在这里添加重试逻辑
  // 或者显示错误提示
  
  // 示例：重试加载
  setTimeout(() => {
    console.log('尝试重新加载路由组件');
    // router.go(0); // 刷新页面
  }, 1000);
}

// ========== 路由工具方法 ==========

/**
 * 动态添加路由
 * @param {Array} newRoutes - 新路由数组
 */
export function addRoutes(newRoutes) {
  newRoutes.forEach(route => {
    router.addRoute(route);
  });
}

/**
 * 动态删除路由
 * @param {string} routeName - 路由名称
 */
export function removeRoute(routeName) {
  router.removeRoute(routeName);
}

/**
 * 获取当前路由信息
 * @returns {Object} 当前路由信息
 */
export function getCurrentRoute() {
  return router.currentRoute;
}

/**
 * 路由跳转
 * @param {string} path - 路径
 * @param {Object} query - 查询参数
 */
export function navigateTo(path, query = {}) {
  router.push({ path, query });
}

/**
 * 路由替换
 * @param {string} path - 路径
 * @param {Object} query - 查询参数
 */
export function replaceTo(path, query = {}) {
  router.replace({ path, query });
}

/**
 * 路由返回
 * @param {number} step - 返回步数
 */
export function goBack(step = -1) {
  router.go(step);
}

// 导出路由实例
export default router;