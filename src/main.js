/**
 * ZJSC 证据审查系统 - 应用入口模块
 * 
 * Module ID: 0
 * 功能: Webpack应用入口点，负责加载主应用模块
 * 
 * 此模块是整个应用的起点，遵循Webpack模块系统的标准入口模式
 */

// 导入主应用模块 (Module ID: 56d7)
// 主应用模块包含Vue根组件、路由配置、状态管理等核心功能
import mainApp from './main-app';

// 导出微前端生命周期函数
// 这些函数由qiankun微前端框架调用
export const bootstrap = mainApp.bootstrap;
export const mount = mainApp.mount;
export const unmount = mainApp.unmount;
export const update = mainApp.update;

// 支持独立运行模式
// 当不在微前端环境中时，直接启动应用
if (!window.__POWERED_BY_QIANKUN__) {
  mainApp.mount();
}

/**
 * 模块说明:
 * - 在微前端模式下，由qiankun调用生命周期函数
 * - 在独立运行模式下，直接调用mount函数启动应用
 * - 主应用逻辑在main-app模块中实现
 */