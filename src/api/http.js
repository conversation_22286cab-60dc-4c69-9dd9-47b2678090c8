/**
 * ZJSC 证据审查系统 - HTTP 客户端封装
 * 
 * Module ID: d354
 * 功能: Axios HTTP 请求封装，包含拦截器和错误处理
 * 
 * 统一管理HTTP请求，提供统一的错误处理和响应格式化
 */

import axios from 'axios';
import store from '@/store';

// ========== 配置 ==========

// 默认配置
const defaultConfig = {
  baseURL: process.env.VUE_APP_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  },
  responseType: 'json',
  withCredentials: true
};

// 请求重试配置
const retryConfig = {
  retries: 3,
  retryDelay: 1000,
  retryCondition: (error) => {
    // 只对网络错误或5xx错误进行重试
    return !error.response || (error.response.status >= 500 && error.response.status < 600);
  }
};

// ========== HTTP客户端类 ==========

class HttpClient {
  constructor(config = {}) {
    this.config = { ...defaultConfig, ...config };
    this.instance = axios.create(this.config);
    this.setupInterceptors();
    this.setupRetry();
  }

  /**
   * 设置拦截器
   */
  setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 显示loading
        store.dispatch('showLoading');
        
        // 添加token
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        
        // 添加请求ID
        config.headers['X-Request-ID'] = this.generateRequestId();
        
        // 添加时间戳
        config.headers['X-Timestamp'] = Date.now();
        
        console.log('HTTP请求:', {
          method: config.method,
          url: config.url,
          data: config.data,
          headers: config.headers
        });
        
        return config;
      },
      (error) => {
        store.dispatch('hideLoading');
        console.error('请求拦截器错误:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        // 隐藏loading
        store.dispatch('hideLoading');
        
        console.log('HTTP响应:', {
          status: response.status,
          url: response.config.url,
          data: response.data
        });
        
        // 统一响应格式
        return this.formatResponse(response);
      },
      (error) => {
        // 隐藏loading
        store.dispatch('hideLoading');
        
        // 处理错误
        return this.handleError(error);
      }
    );
  }

  /**
   * 设置重试机制
   */
  setupRetry() {
    this.instance.interceptors.response.use(
      (response) => response,
      async (error) => {
        const config = error.config;
        
        if (!config || !config.retryCount) {
          return Promise.reject(error);
        }
        
        config.retryCount = config.retryCount || 0;
        
        if (config.retryCount >= retryConfig.retries) {
          return Promise.reject(error);
        }
        
        if (!retryConfig.retryCondition(error)) {
          return Promise.reject(error);
        }
        
        config.retryCount += 1;
        
        // 延迟重试
        await new Promise(resolve => {
          setTimeout(resolve, retryConfig.retryDelay);
        });
        
        console.log(`请求重试 (${config.retryCount}/${retryConfig.retries}):`, config.url);
        
        return this.instance(config);
      }
    );
  }

  /**
   * 格式化响应
   * @param {Object} response - Axios响应对象
   * @returns {Object} 格式化后的响应
   */
  formatResponse(response) {
    const { data, status, statusText, headers } = response;
    
    // 标准响应格式
    const standardResponse = {
      success: true,
      code: status,
      message: statusText,
      data: data.data || data,
      timestamp: Date.now(),
      headers: headers
    };
    
    // 处理业务状态码
    if (data.code !== undefined) {
      standardResponse.code = data.code;
      standardResponse.message = data.message || data.msg || statusText;
      standardResponse.success = data.code === 200 || data.code === 0;
    }
    
    return standardResponse;
  }

  /**
   * 处理错误
   * @param {Error} error - 错误对象
   * @returns {Promise} 拒绝的Promise
   */
  handleError(error) {
    console.error('HTTP请求错误:', error);
    
    let errorResponse = {
      success: false,
      code: 500,
      message: '网络请求失败',
      data: null,
      timestamp: Date.now()
    };
    
    if (error.response) {
      // 服务器响应错误
      const { status, data, statusText } = error.response;
      
      errorResponse = {
        success: false,
        code: status,
        message: data?.message || data?.msg || statusText,
        data: data,
        timestamp: Date.now()
      };
      
      // 处理特定状态码
      switch (status) {
        case 401:
          this.handleUnauthorized();
          break;
        case 403:
          this.handleForbidden();
          break;
        case 404:
          this.handleNotFound();
          break;
        case 500:
          this.handleServerError();
          break;
      }
    } else if (error.request) {
      // 请求未到达服务器
      errorResponse = {
        success: false,
        code: 0,
        message: '网络连接失败，请检查网络设置',
        data: null,
        timestamp: Date.now()
      };
    } else {
      // 请求配置错误
      errorResponse = {
        success: false,
        code: 0,
        message: error.message || '请求配置错误',
        data: null,
        timestamp: Date.now()
      };
    }
    
    // 显示错误提示
    this.showErrorNotification(errorResponse.message);
    
    // 记录错误日志
    this.logError(error, errorResponse);
    
    return Promise.reject(errorResponse);
  }

  /**
   * 处理401未授权
   */
  handleUnauthorized() {
    console.warn('用户未授权，清除用户信息');
    store.dispatch('resetPersonInfo');
    store.dispatch('resetCaseInfo');
    
    // 跳转到登录页
    if (window.location.pathname !== '/') {
      window.location.href = '/';
    }
  }

  /**
   * 处理403禁止访问
   */
  handleForbidden() {
    console.warn('用户没有权限访问该资源');
    store.dispatch('showNotification', {
      message: '您没有权限访问该资源',
      type: 'error'
    });
  }

  /**
   * 处理404资源不存在
   */
  handleNotFound() {
    console.warn('请求的资源不存在');
    store.dispatch('showNotification', {
      message: '请求的资源不存在',
      type: 'warning'
    });
  }

  /**
   * 处理500服务器错误
   */
  handleServerError() {
    console.error('服务器内部错误');
    store.dispatch('showNotification', {
      message: '服务器内部错误，请稍后重试',
      type: 'error'
    });
  }

  /**
   * 显示错误通知
   * @param {string} message - 错误消息
   */
  showErrorNotification(message) {
    store.dispatch('showNotification', {
      message,
      type: 'error',
      duration: 5000
    });
  }

  /**
   * 记录错误日志
   * @param {Error} error - 原始错误
   * @param {Object} errorResponse - 格式化后的错误响应
   */
  logError(error, errorResponse) {
    const errorLog = {
      type: 'http_error',
      url: error.config?.url,
      method: error.config?.method,
      status: errorResponse.code,
      message: errorResponse.message,
      timestamp: errorResponse.timestamp,
      userAgent: navigator.userAgent,
      stack: error.stack
    };
    
    console.error('错误日志:', errorLog);
    
    // 可以发送到错误收集服务
    // this.sendErrorToServer(errorLog);
  }

  /**
   * 发送错误到服务器
   * @param {Object} errorLog - 错误日志
   */
  sendErrorToServer(errorLog) {
    this.instance.post('/logs/error', errorLog).catch(err => {
      console.error('错误日志发送失败:', err);
    });
  }

  /**
   * 获取token
   * @returns {string|null} token
   */
  getToken() {
    // 从store获取token
    const state = store.state;
    return state.personInfo?.token || state.system?.token;
  }

  /**
   * 生成请求ID
   * @returns {string} 请求ID
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // ========== HTTP方法 ==========

  /**
   * GET请求
   * @param {string} url - 请求地址
   * @param {Object} params - 查询参数
   * @param {Object} config - 请求配置
   * @returns {Promise} 响应Promise
   */
  get(url, params = {}, config = {}) {
    return this.instance.get(url, { ...config, params });
  }

  /**
   * POST请求
   * @param {string} url - 请求地址
   * @param {Object} data - 请求数据
   * @param {Object} config - 请求配置
   * @returns {Promise} 响应Promise
   */
  post(url, data = {}, config = {}) {
    return this.instance.post(url, data, config);
  }

  /**
   * PUT请求
   * @param {string} url - 请求地址
   * @param {Object} data - 请求数据
   * @param {Object} config - 请求配置
   * @returns {Promise} 响应Promise
   */
  put(url, data = {}, config = {}) {
    return this.instance.put(url, data, config);
  }

  /**
   * DELETE请求
   * @param {string} url - 请求地址
   * @param {Object} config - 请求配置
   * @returns {Promise} 响应Promise
   */
  delete(url, config = {}) {
    return this.instance.delete(url, config);
  }

  /**
   * PATCH请求
   * @param {string} url - 请求地址
   * @param {Object} data - 请求数据
   * @param {Object} config - 请求配置
   * @returns {Promise} 响应Promise
   */
  patch(url, data = {}, config = {}) {
    return this.instance.patch(url, data, config);
  }

  /**
   * 文件上传
   * @param {string} url - 上传地址
   * @param {FormData} formData - 表单数据
   * @param {Object} config - 请求配置
   * @returns {Promise} 响应Promise
   */
  upload(url, formData, config = {}) {
    const uploadConfig = {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config.headers
      },
      onUploadProgress: (progressEvent) => {
        if (config.onUploadProgress) {
          config.onUploadProgress(progressEvent);
        }
      }
    };
    
    return this.instance.post(url, formData, uploadConfig);
  }

  /**
   * 文件下载
   * @param {string} url - 下载地址
   * @param {Object} params - 查询参数
   * @param {Object} config - 请求配置
   * @returns {Promise} 响应Promise
   */
  download(url, params = {}, config = {}) {
    const downloadConfig = {
      ...config,
      responseType: 'blob',
      params
    };
    
    return this.instance.get(url, downloadConfig);
  }

  /**
   * 批量请求
   * @param {Array} requests - 请求数组
   * @returns {Promise} 响应Promise数组
   */
  all(requests) {
    return Promise.all(requests);
  }

  /**
   * 取消请求
   * @param {string} url - 请求地址
   */
  cancel(url) {
    // 可以实现请求取消逻辑
    console.log('取消请求:', url);
  }
}

// ========== 创建实例 ==========

// 创建默认HTTP客户端
const httpClient = new HttpClient();

// 创建专用HTTP客户端
const createHttpClient = (config) => {
  return new HttpClient(config);
};

// ========== 导出 ==========

export {
  HttpClient,
  httpClient,
  createHttpClient
};

export default httpClient;