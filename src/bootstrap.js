/**
 * ZJSC 证据审查系统 - 应用初始化配置
 * 
 * Module ID: a178
 * 功能: 应用初始化配置和启动脚本，包含环境检测、依赖检查等
 * 
 * 负责应用的启动前准备工作
 */

/**
 * 应用配置对象
 */
const appConfig = {
  // 应用基本信息
  name: 'ZJSC证据审查系统',
  version: '1.0.0',
  description: '司法检察证据审查系统前端应用',
  
  // 环境配置
  environment: process.env.NODE_ENV || 'development',
  
  // API配置
  api: {
    baseURL: process.env.VUE_APP_API_BASE_URL || '/api',
    timeout: 30000,
    retryCount: 3
  },
  
  // 路由配置
  router: {
    mode: 'history',
    base: process.env.VUE_APP_PUBLIC_PATH || '/',
    fallback: true
  },
  
  // 状态管理配置
  store: {
    persistence: true,
    storage: 'sessionStorage'
  },
  
  // UI配置
  ui: {
    elementUI: {
      size: 'small',
      zIndex: 2000
    },
    theme: {
      primaryColor: '#409EFF',
      successColor: '#67C23A',
      warningColor: '#E6A23C',
      dangerColor: '#F56C6C',
      infoColor: '#909399'
    }
  },
  
  // 微前端配置
  microFrontend: {
    enabled: true,
    sandbox: true,
    strictStyleIsolation: false
  },
  
  // 功能开关
  features: {
    wpsIntegration: true,
    mindMap: true,
    pdfPreview: true,
    offlineMode: false
  }
};

/**
 * 初始化应用配置
 * @returns {Promise<Object>} 配置对象
 */
export async function initializeApp() {
  console.log('开始初始化应用配置...');
  
  try {
    // 检测运行环境
    const environment = detectEnvironment();
    
    // 检查依赖
    await checkDependencies();
    
    // 配置全局变量
    configureGlobals(environment);
    
    // 初始化错误处理
    initializeErrorHandling();
    
    // 初始化性能监控
    initializePerformanceMonitoring();
    
    console.log('应用配置初始化完成');
    
    return {
      ...appConfig,
      environment,
      initialized: true,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('应用配置初始化失败:', error);
    throw error;
  }
}

/**
 * 检测运行环境
 * @returns {Object} 环境信息
 */
function detectEnvironment() {
  const environment = {
    isBrowser: typeof window !== 'undefined',
    isNode: typeof process !== 'undefined' && process.versions && process.versions.node,
    isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
    isQiankun: window.__POWERED_BY_QIANKUN__,
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production'
  };
  
  console.log('环境检测结果:', environment);
  return environment;
}

/**
 * 检查依赖
 * @returns {Promise<void>}
 */
async function checkDependencies() {
  const requiredDependencies = [
    'Vue',
    'VueRouter',
    'Vuex',
    'ElementUI'
  ];
  
  const missingDependencies = [];
  
  for (const dep of requiredDependencies) {
    if (!window[dep] && !global[dep]) {
      missingDependencies.push(dep);
    }
  }
  
  if (missingDependencies.length > 0) {
    throw new Error(`缺少必要的依赖: ${missingDependencies.join(', ')}`);
  }
  
  console.log('依赖检查通过');
}

/**
 * 配置全局变量
 * @param {Object} environment - 环境信息
 */
function configureGlobals(environment) {
  // 设置全局配置
  window.ZJSC_CONFIG = appConfig;
  window.ZJSC_ENV = environment;
  
  // 设置全局错误处理
  window.onerror = function(message, source, lineno, colno, error) {
    console.error('全局错误:', { message, source, lineno, colno, error });
    
    // 可以在这里添加错误上报逻辑
    reportError({
      type: 'global_error',
      message,
      source,
      lineno,
      colno,
      stack: error?.stack
    });
    
    return false;
  };
  
  // 设置未处理的Promise rejection处理
  window.addEventListener('unhandledrejection', function(event) {
    console.error('未处理的Promise rejection:', event.reason);
    
    reportError({
      type: 'promise_rejection',
      reason: event.reason,
      promise: event.promise
    });
  });
  
  console.log('全局变量配置完成');
}

/**
 * 初始化错误处理
 */
function initializeErrorHandling() {
  // Vue错误处理
  if (window.Vue) {
    Vue.config.errorHandler = function(err, vm, info) {
      console.error('Vue错误:', err);
      console.error('组件:', vm);
      console.error('错误信息:', info);
      
      reportError({
        type: 'vue_error',
        error: err,
        component: vm?.$options.name,
        info
      });
    };
  }
  
  console.log('错误处理初始化完成');
}

/**
 * 初始化性能监控
 */
function initializePerformanceMonitoring() {
  if (window.performance && window.performance.getEntriesByType) {
    // 监听页面加载性能
    window.addEventListener('load', function() {
      const perfData = performance.getEntriesByType('navigation')[0];
      console.log('页面加载性能:', {
        domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
        loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
        firstPaint: perfData.responseEnd - perfData.fetchStart
      });
    });
  }
  
  console.log('性能监控初始化完成');
}

/**
 * 错误上报
 * @param {Object} errorInfo - 错误信息
 */
function reportError(errorInfo) {
  // 这里可以实现错误上报到服务器的逻辑
  console.log('错误上报:', errorInfo);
  
  // 示例：发送到错误收集服务
  if (appConfig.api.baseURL) {
    // fetch(`${appConfig.api.baseURL}/errors`, {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json'
    //   },
    //   body: JSON.stringify(errorInfo)
    // }).catch(err => {
    //   console.error('错误上报失败:', err);
    // });
  }
}

/**
 * 获取应用配置
 * @returns {Object} 应用配置
 */
export function getAppConfig() {
  return appConfig;
}

/**
 * 更新应用配置
 * @param {Object} newConfig - 新配置
 */
export function updateAppConfig(newConfig) {
  Object.assign(appConfig, newConfig);
  console.log('应用配置已更新:', newConfig);
}

/**
 * 检查功能是否启用
 * @param {string} feature - 功能名称
 * @returns {boolean} 是否启用
 */
export function isFeatureEnabled(feature) {
  return !!appConfig.features[feature];
}

// 导出默认配置
export default appConfig;