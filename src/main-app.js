/**
 * ZJSC 证据审查系统 - 主应用模块
 * 
 * Module ID: 56d7
 * 功能: 整合Vue应用、路由配置、状态管理和微前端生命周期
 * 
 * 这是应用的核心模块，包含完整的Vue应用架构
 */

import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import { bootstrap as lifecycleBootstrap, mount as lifecycleMount, unmount as lifecycleUnmount, update as lifecycleUpdate } from './lifecycle';

// 全局应用实例
let app = null;

/**
 * 渲染应用
 * @param {Object} props - 应用属性
 */
function renderApp(props) {
  app = new Vue({
    router,
    store,
    render: h => h(App)
  }).$mount('#app');
  
  console.log('ZJSC 应用已渲染');
}

/**
 * 微前端生命周期 - 启动钩子
 * @param {Object} props - 主应用传递的属性
 */
async function bootstrap(props) {
  console.log('ZJSC 微前端应用启动', props);
  
  // 调用生命周期模块的启动逻辑
  if (lifecycleBootstrap) {
    await lifecycleBootstrap(props);
  }
}

/**
 * 微前端生命周期 - 挂载钩子
 * @param {Object} props - 主应用传递的属性
 */
async function mount(props) {
  console.log('ZJSC 微前端应用挂载', props);
  
  // 设置全局通信
  if (props && window.qiankunActions) {
    window.qiankunActions.setActions(props);
  }
  
  // 监听全局状态变化
  if (props && props.onGlobalStateChange) {
    props.onGlobalStateChange((state, prevState) => {
      console.log('全局状态变化:', state, prevState);
    });
  }
  
  // 设置全局状态
  if (props && props.setGlobalState) {
    props.setGlobalState({
      event: 'opendialog'
    });
  }
  
  // 调用生命周期模块的挂载逻辑
  if (lifecycleMount) {
    await lifecycleMount(props);
  }
  
  // 渲染应用
  renderApp(props);
}

/**
 * 微前端生命周期 - 卸载钩子
 * @param {Object} props - 主应用传递的属性
 */
async function unmount(props) {
  console.log('ZJSC 微前端应用卸载', props);
  
  // 调用生命周期模块的卸载逻辑
  if (lifecycleUnmount) {
    await lifecycleUnmount(props);
  }
  
  // 销毁Vue应用
  if (app) {
    app.$destroy();
    app = null;
  }
}

/**
 * 微前端生命周期 - 更新钩子
 * @param {Object} props - 主应用传递的属性
 */
async function update(props) {
  console.log('ZJSC 微前端应用更新', props);
  
  // 调用生命周期模块的更新逻辑
  if (lifecycleUpdate) {
    await lifecycleUpdate(props);
  }
}

// 导出微前端生命周期函数
export {
  bootstrap,
  mount,
  unmount,
  update
};

// 导出渲染函数供独立运行使用
export { renderApp };

// 导出核心实例供调试使用
export { app, router, store };