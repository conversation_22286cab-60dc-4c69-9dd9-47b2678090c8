/**
 * ZJSC 证据审查系统 - 微前端生命周期管理
 * 
 * Module ID: 8b65
 * 功能: 微前端生命周期函数管理，包含应用启动、挂载、卸载、更新等逻辑
 * 
 * 为qiankun微前端框架提供生命周期支持
 */

// 全局状态管理
let globalProps = null;

/**
 * 微前端生命周期 - 启动
 * @param {Object} props - 主应用传递的属性
 * @returns {Promise<void>}
 */
export async function bootstrap(props) {
  console.log('ZJSC 生命周期: 启动应用', props);
  
  // 保存全局属性
  globalProps = props;
  
  // 设置动态publicPath（微前端环境）
  if (window.__POWERED_BY_QIANKUN__) {
    __webpack_public_path__ = window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;
  }
  
  // 初始化全局通信机制
  if (props) {
    window.qiankunActions = {
      setActions: (actions) => {
        window.qiankunActions = { ...window.qiankunActions, ...actions };
      },
      getProps: () => props
    };
  }
  
  // 应用级别的初始化逻辑
  await initializeApplication();
}

/**
 * 微前端生命周期 - 挂载
 * @param {Object} props - 主应用传递的属性
 * @returns {Promise<void>}
 */
export async function mount(props) {
  console.log('ZJSC 生命周期: 挂载应用', props);
  
  // 更新全局属性
  globalProps = props;
  
  // 设置全局事件监听
  setupGlobalEventListeners(props);
  
  // 初始化路由和状态
  await initializeRouterAndState(props);
  
  // 触发应用挂载事件
  emitAppEvent('mounted', props);
}

/**
 * 微前端生命周期 - 卸载
 * @param {Object} props - 主应用传递的属性
 * @returns {Promise<void>}
 */
export async function unmount(props) {
  console.log('ZJSC 生命周期: 卸载应用', props);
  
  // 清理全局事件监听
  cleanupGlobalEventListeners();
  
  // 清理路由和状态
  await cleanupRouterAndState();
  
  // 清理定时器和异步任务
  cleanupAsyncTasks();
  
  // 触发应用卸载事件
  emitAppEvent('unmounted', props);
  
  // 清理全局属性
  globalProps = null;
}

/**
 * 微前端生命周期 - 更新
 * @param {Object} props - 主应用传递的属性
 * @returns {Promise<void>}
 */
export async function update(props) {
  console.log('ZJSC 生命周期: 更新应用', props);
  
  // 更新全局属性
  globalProps = props;
  
  // 处理属性更新
  handlePropsUpdate(props);
  
  // 触发应用更新事件
  emitAppEvent('updated', props);
}

// ========== 私有方法 ==========

/**
 * 初始化应用
 */
async function initializeApplication() {
  // 预加载必要的资源
  await preloadResources();
  
  // 初始化配置
  initializeConfig();
  
  console.log('应用初始化完成');
}

/**
 * 预加载资源
 */
async function preloadResources() {
  // 这里可以预加载字体、图片等静态资源
  return Promise.resolve();
}

/**
 * 初始化配置
 */
function initializeConfig() {
  // 设置应用级别的配置
  window.ZJSC_CONFIG = {
    appName: 'ZJSC证据审查系统',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  };
}

/**
 * 设置全局事件监听
 * @param {Object} props - 主应用属性
 */
function setupGlobalEventListeners(props) {
  if (props && props.onGlobalStateChange) {
    // 监听全局状态变化
    props.onGlobalStateChange((state, prevState) => {
      console.log('全局状态变化:', { state, prevState });
      handleGlobalStateChange(state, prevState);
    });
  }
  
  // 监听窗口事件
  window.addEventListener('resize', handleWindowResize);
  
  // 监听路由变化
  if (window.addEventListener) {
    window.addEventListener('popstate', handleRouteChange);
  }
}

/**
 * 清理全局事件监听
 */
function cleanupGlobalEventListeners() {
  window.removeEventListener('resize', handleWindowResize);
  
  if (window.removeEventListener) {
    window.removeEventListener('popstate', handleRouteChange);
  }
}

/**
 * 初始化路由和状态
 */
async function initializeRouterAndState(props) {
  // 导入微前端状态管理工具
  const microStateManager = await import('@/utils/micro-state')

  // 设置全局状态管理 actions
  if (props && microStateManager.default) {
    microStateManager.default.setActions(props)

    // 监听全局状态变化
    microStateManager.default.onGlobalStateChange((newState, prevState) => {
      console.log('全局状态变化:', { newState, prevState })
      handleGlobalStateChange(newState, prevState)
    })

    // 设置初始全局状态
    microStateManager.default.setGlobalState({
      event: 'opendialog',
      appName: 'zjsc',
      timestamp: Date.now()
    })
  }
}

/**
 * 清理路由和状态
 */
async function cleanupRouterAndState() {
  // 清理路由和状态逻辑
}

/**
 * 清理异步任务
 */
function cleanupAsyncTasks() {
  // 清理所有定时器
  const highestTimeoutId = setTimeout(';');
  for (let i = 0; i < highestTimeoutId; i++) {
    clearTimeout(i);
  }
  
  // 清理所有interval
  const highestIntervalId = setInterval(';');
  for (let i = 0; i < highestIntervalId; i++) {
    clearInterval(i);
  }
}

/**
 * 处理全局状态变化
 * @param {Object} state - 新状态
 * @param {Object} prevState - 旧状态
 */
function handleGlobalStateChange(state, prevState) {
  // 处理全局状态变化逻辑
  console.log('处理全局状态变化:', { state, prevState });
}

/**
 * 处理属性更新
 * @param {Object} props - 更新的属性
 */
function handlePropsUpdate(props) {
  // 处理属性更新逻辑
  console.log('处理属性更新:', props);
}

/**
 * 处理窗口大小变化
 */
function handleWindowResize() {
  // 处理窗口大小变化逻辑
  console.log('窗口大小变化');
}

/**
 * 处理路由变化
 */
function handleRouteChange() {
  // 处理路由变化逻辑
  console.log('路由变化');
}

/**
 * 触发应用事件
 * @param {string} eventName - 事件名称
 * @param {Object} data - 事件数据
 */
function emitAppEvent(eventName, data) {
  // 触发自定义事件
  const event = new CustomEvent(`zjsc:${eventName}`, {
    detail: data,
    bubbles: true,
    cancelable: true
  });
  
  window.dispatchEvent(event);
  console.log(`触发应用事件: ${eventName}`, data);
}

// ========== 工具方法 ==========

/**
 * 获取全局属性
 * @returns {Object} 全局属性
 */
export function getGlobalProps() {
  return globalProps;
}

/**
 * 检查是否在微前端环境中
 * @returns {boolean} 是否在微前端环境中
 */
export function isInQiankun() {
  return window.__POWERED_BY_QIANKUN__;
}

/**
 * 设置全局状态
 * @param {Object} state - 要设置的状态
 */
export function setGlobalState(state) {
  if (globalProps && globalProps.setGlobalState) {
    globalProps.setGlobalState(state);
  }
}